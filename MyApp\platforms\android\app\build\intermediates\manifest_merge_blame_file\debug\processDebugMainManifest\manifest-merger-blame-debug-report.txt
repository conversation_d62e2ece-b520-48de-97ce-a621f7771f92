1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.ctfs.wicimobile"
4    android:hardwareAccelerated="true"
5    android:versionCode="10000"
6    android:versionName="1.0.0" >
7
8    <uses-sdk
9        android:minSdkVersion="24"
10        android:targetSdkVersion="35" />
11
12    <supports-screens
12-->C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:3:5-191
13        android:anyDensity="true"
13-->C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:3:23-48
14        android:largeScreens="true"
14-->C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:3:49-76
15        android:normalScreens="true"
15-->C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:3:77-105
16        android:resizeable="true"
16-->C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:3:106-131
17        android:smallScreens="true"
17-->C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:3:132-159
18        android:xlargeScreens="true" />
18-->C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:3:160-188
19
20    <uses-permission android:name="android.permission.INTERNET" />
20-->C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:4:5-67
20-->C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:4:22-64
21
22    <queries>
22-->C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:16:5-20:15
23        <intent>
23-->C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:17:9-19:18
24            <action android:name="android.media.action.IMAGE_CAPTURE" />
24-->C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:18:13-73
24-->C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:18:21-70
25        </intent>
26    </queries>
27
28    <permission
28-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7c013e1f43d1815644395c70a32bee83\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
29        android:name="com.ctfs.wicimobile.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
29-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7c013e1f43d1815644395c70a32bee83\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
30        android:protectionLevel="signature" />
30-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7c013e1f43d1815644395c70a32bee83\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
31
32    <uses-permission android:name="com.ctfs.wicimobile.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
32-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7c013e1f43d1815644395c70a32bee83\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
32-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7c013e1f43d1815644395c70a32bee83\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
33
34    <application
34-->C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:5:5-15:19
35        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
35-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7c013e1f43d1815644395c70a32bee83\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
36        android:debuggable="true"
37        android:extractNativeLibs="false"
38        android:hardwareAccelerated="true"
38-->C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:5:18-52
39        android:icon="@mipmap/ic_launcher"
39-->C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:5:53-87
40        android:label="@string/app_name"
40-->C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:5:88-120
41        android:supportsRtl="true" >
41-->C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:5:121-147
42        <activity
42-->C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:6:9-11:20
43            android:name="com.ctfs.wicimobile.MainActivity"
43-->C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:6:240-267
44            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|smallestScreenSize|screenLayout|uiMode|navigation"
44-->C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:6:19-146
45            android:exported="true"
45-->C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:6:147-170
46            android:label="@string/activity_name"
46-->C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:6:171-208
47            android:launchMode="singleTop"
47-->C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:6:209-239
48            android:theme="@style/Theme.App.SplashScreen"
48-->C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:6:268-313
49            android:windowSoftInputMode="adjustResize" >
49-->C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:6:314-356
50            <intent-filter android:label="@string/launcher_name" >
50-->C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:7:13-10:29
50-->C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:7:28-65
51                <action android:name="android.intent.action.MAIN" />
51-->C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:8:17-69
51-->C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:8:25-66
52
53                <category android:name="android.intent.category.LAUNCHER" />
53-->C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:9:17-77
53-->C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:9:27-74
54            </intent-filter>
55        </activity>
56
57        <provider
58            android:name="androidx.core.content.FileProvider"
58-->C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:12:141-190
59            android:authorities="com.ctfs.wicimobile.cdv.core.file.provider"
59-->C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:12:19-80
60            android:exported="false"
60-->C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:12:81-105
61            android:grantUriPermissions="true" >
61-->C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:12:106-140
62            <meta-data
62-->C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:13:13-130
63                android:name="android.support.FILE_PROVIDER_PATHS"
63-->C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:13:24-74
64                android:resource="@xml/cdv_core_file_provider_paths" />
64-->C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:13:75-127
65        </provider>
66        <provider
66-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\537fe04c7b6e13d2c64226c263643d00\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
67            android:name="androidx.startup.InitializationProvider"
67-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\537fe04c7b6e13d2c64226c263643d00\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
68            android:authorities="com.ctfs.wicimobile.androidx-startup"
68-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\537fe04c7b6e13d2c64226c263643d00\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
69            android:exported="false" >
69-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\537fe04c7b6e13d2c64226c263643d00\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
70            <meta-data
70-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\537fe04c7b6e13d2c64226c263643d00\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
71                android:name="androidx.emoji2.text.EmojiCompatInitializer"
71-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\537fe04c7b6e13d2c64226c263643d00\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
72                android:value="androidx.startup" />
72-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\537fe04c7b6e13d2c64226c263643d00\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
73            <meta-data
73-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\43d493f403f3ed0382eb0f1c07b66ab5\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
74                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
74-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\43d493f403f3ed0382eb0f1c07b66ab5\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
75                android:value="androidx.startup" />
75-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\43d493f403f3ed0382eb0f1c07b66ab5\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
76            <meta-data
76-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\42e3f4e99d8fe2fc7fff39091a76a22b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
77                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
77-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\42e3f4e99d8fe2fc7fff39091a76a22b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
78                android:value="androidx.startup" />
78-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\42e3f4e99d8fe2fc7fff39091a76a22b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
79        </provider>
80
81        <receiver
81-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\42e3f4e99d8fe2fc7fff39091a76a22b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
82            android:name="androidx.profileinstaller.ProfileInstallReceiver"
82-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\42e3f4e99d8fe2fc7fff39091a76a22b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
83            android:directBootAware="false"
83-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\42e3f4e99d8fe2fc7fff39091a76a22b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
84            android:enabled="true"
84-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\42e3f4e99d8fe2fc7fff39091a76a22b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
85            android:exported="true"
85-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\42e3f4e99d8fe2fc7fff39091a76a22b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
86            android:permission="android.permission.DUMP" >
86-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\42e3f4e99d8fe2fc7fff39091a76a22b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
87            <intent-filter>
87-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\42e3f4e99d8fe2fc7fff39091a76a22b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
88                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
88-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\42e3f4e99d8fe2fc7fff39091a76a22b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
88-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\42e3f4e99d8fe2fc7fff39091a76a22b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
89            </intent-filter>
90            <intent-filter>
90-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\42e3f4e99d8fe2fc7fff39091a76a22b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
91                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
91-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\42e3f4e99d8fe2fc7fff39091a76a22b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
91-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\42e3f4e99d8fe2fc7fff39091a76a22b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
92            </intent-filter>
93            <intent-filter>
93-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\42e3f4e99d8fe2fc7fff39091a76a22b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
94                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
94-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\42e3f4e99d8fe2fc7fff39091a76a22b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
94-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\42e3f4e99d8fe2fc7fff39091a76a22b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
95            </intent-filter>
96            <intent-filter>
96-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\42e3f4e99d8fe2fc7fff39091a76a22b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
97                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
97-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\42e3f4e99d8fe2fc7fff39091a76a22b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
97-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\42e3f4e99d8fe2fc7fff39091a76a22b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
98            </intent-filter>
99        </receiver>
100    </application>
101
102</manifest>
