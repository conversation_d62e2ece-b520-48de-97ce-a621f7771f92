<!DOCTYPE html>
<html>
    <head>
        <link rel="shortcut icon" href="app/images/favicon.ico"/>
        <meta charset="UTF-8">
		<meta name="viewport" id="viewport" content="width=device-width, height=device-height,initial-scale=1.0, maximum-scale=1.0,user-scalable=no" />
        <!--       <meta http-equiv="Content-Security-Policy" content="default-src *; style-src 'self' 'unsafe-inline';img-src 'self' data: ;script-src 'self' 'unsafe-inline' 'unsafe-eval' http://blr-ctfs01.nessinblr.com:9080/WICIMiddlewareBroker2/ "> -->
        <!--       <meta name="format-detection" content="telephone=no"> -->
        <!--       <meta name="msapplication-tap-highlight" content="no"> -->
		<link rel="stylesheet" href="https://ws1.postescanada-canadapost.ca/css/addresscomplete-2.30.min.css?key=gz38-nt61-zz89-db43" type="text/css">
		<script src="https://ws1.postescanada-canadapost.ca/js/addresscomplete-2.30.min.js?key=gz38-nt61-zz89-db43" type="text/javascript" ></script>    
        <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.0.487/pdf.min.js"></script>
        <!--CSS//-->
        <link rel="stylesheet" type="text/css" href="app/wiciwebapp.css"></link>

        <!--Application-related JavaScript Files//-->
        <script src="app/cordova.js" type="text/javascript"></script>
        <script src="app/wiciwebapp.js" type="text/javascript"></script>
 
<script id="BreadcrumbTrail-template" type="text/x-jquery-tmpl">
<div id="BreadcrumbTrailArea" class="BreadcrumbTrailAreaLayoutStyle">
	<ul id="ID_BreadcrumbTrail" class="BreadcrumbTrail">
		<li><a href="#"><span class="BreadcrumbTrailItemNumberFont">1.</span><span class="BreadcrumbTrailItemFont" data-translationKey="breadCrumbItem_ProductSelection"></span></a></li>
		<li><a href="#"><span class="BreadcrumbTrailItemNumberFont">2.</span><span class="BreadcrumbTrailItemFont" data-translationKey="breadCrumbItem_ApplicantInfo"></span></a></li>
		<li><a href="#"><span class="BreadcrumbTrailItemNumberFont">3.</span><span class="BreadcrumbTrailItemFont" data-translationKey="breadCrumbItem_FinancialAndEmploymentInfo"></span></a></li>
		<li><a href="#"><span class="BreadcrumbTrailItemNumberFont">4.</span><span class="BreadcrumbTrailItemFont" data-translationKey="breadCrumbItem_SupplementaryCard "></span></a></li>
		<li><a href="#"><span class="BreadcrumbTrailItemNumberFont">5.</span><span class="BreadcrumbTrailItemFont" data-translationKey="breadCrumbItem_OptionalProducts"></span></a></li>
		<li><a href="#"><span class="BreadcrumbTrailItemNumberFont"></span><span class="BreadcrumbTrailItemFont" data-translationKey="breadCrumbItem_Confirmation"></span></a></li>
	</ul>
</div>
</script>
<script id="canadaPostAddressNotFoundMessageDialog-template" type="text/x-jquery-tmpl">
<div data-role="dialog" id="${messageDialogId}">	
	<div data-role="header" data-theme="d" class="ui-corner-top ui-overlay-shadow ui-header ui-bar-d" role="banner">
		<h1 class="ui-title" tabindex="0" role="heading" aria-level="1">${title}</h1>
	</div>
	<div data-role="content" data-theme="c" class="ui-overlay-shadow ui-corner-bottom ui-content ui-body-c dialogFixes" role="main">
		<br>
		<div class="canadaPostTitleFont paddingLeft40">
			{{html scannedAddressTitle}}
		</div>
		<div class="canadaPostDetailsFont paddingLeft80">
			{{html message}}
		</div>
		<div class="canadaPostTitleFont paddingLeft40">
			{{html messageBottom}}
		</div>
		<br>
		<div class="dialogButtonContainer">
			<a href="#" data-rel="back" id="messageDialogOkay" data-role="button" data-theme="c" class="ui-btn ui-shadow ui-btn-corner-all ui-btn-up-c">
				<span class="ui-btn-inner ui-btn-corner-all" aria-hidden="true">
					<span class="ui-btn-text">${button}</span> 
				</span> 
			</a>
		</div>
	</div>
</div>
</script>
<script id="canadaPostMessageDialog-template" type="text/x-jquery-tmpl">
<div data-role="dialog" id="messageDialog" class="ui-page ui-body-c ui-overlay-a ui-dialog ui-page-active" role="dialog">
	<div data-role="header" data-theme="d" class="ui-corner-top ui-overlay-shadow ui-header ui-bar-d" role="banner">
		<h1 class="ui-title" tabindex="0" role="heading" aria-level="1">${title}</h1>
	</div>
	<div data-role="content" data-theme="c"	class="ui-overlay-shadow ui-corner-bottom ui-content ui-body-c dialogFixes" role="main">
		<p class="paddingLeft35">
			<br>
			<span class="canadaPostTitleFont">
				{{html scannedAddressTitle}}
			</span>
			<span class="canadaPostDetailsFont">
				{{html scannedAddressMessage}}
			</span>
			<br>
			<span class="canadaPostTitleFont">
				{{html canadaPostTitle}}
			</span>
			<span class="canadaPostDetailsFont">
				{{html canadaPostMessage}}
			</span>
			<br>
		</p>
		<div class="dialogButtonContainer">
			<a href="#" id="confirm_confirmButton" data-rel="back" data-role="button" data-theme="c">${acceptButton}</a>
			<a href="#" id="confirm_cancelButton" data-rel="back" data-role="button" data-theme="c">${continueButton}</a>
		</div>
	</div>
</div>
</script>
<script id="confirmMessageDialog-template" type="text/x-jquery-tmpl">
<div data-role="dialog" id="messageDialog" >
	<div data-role="header" data-theme="d" class="ui-corner-top ui-overlay-shadow ui-header ui-bar-d" role="banner">
		<h1 class="ui-title" tabindex="0" role="heading" aria-level="1">${title}</h1>
	</div>
	<div data-role="content" data-theme="c"	class="ui-overlay-shadow ui-corner-bottom ui-content ui-body-c dialogFixes" role="main">
		<p>
			<center>
				<br>
				<span class="locationDetailsFont">
					${message}
				</span>
				<br>
				<br>
			</center>
		<div class="dialogButtonContainer">
			<a href="#" id="confirm_confirmButton" data-rel="back" data-role="button" data-theme="c">${yesButton}</a>
			<a href="#" id="confirm_cancelButton" data-rel="back" data-role="button" data-theme="c">${noButton}</a>
		</div>
	</div>
</div>
</script>

<script id="confirmMessageForAgentDialog-template" type="text/x-jquery-tmpl">
<div data-role="dialog" id="messageDialog" >
	<div data-role="header" data-theme="d" class="ui-corner-top ui-overlay-shadow ui-header ui-bar-d" role="banner">
		<h1 class="ui-title" tabindex="0" role="heading" aria-level="1">${title}</h1>
	</div>
	<div data-role="content" data-theme="c"	class="ui-overlay-shadow ui-corner-bottom ui-content ui-body-c dialogFixes" role="main">
		<p>
			<center>
				<br>
				<span class="locationDetailsFont">
					${messageOne}
				</span>
				<br>
				<span class="locationDetailsFont">
					${messageTwo}
				</span>
				<br>
				<br>
			</center>
		<div class="dialogButtonContainer">
			<a href="#" id="agentConfirm_confirmButton" data-rel="back" data-role="button" data-theme="c">${yesButton}</a>
			<a href="#" id="agentConfirm_cancelButton" data-rel="back" data-role="button" data-theme="c">${noButton}</a>
		</div>
	</div>
</div>
</script>

<script id="htmlConfirmMessageDialog-template" type="text/x-jquery-tmpl">
<div data-role="dialog" id="messageDialog" class="ui-page ui-body-c ui-overlay-a ui-dialog ui-page-active" role="dialog">
	<div data-role="header" data-theme="d" class="ui-corner-top ui-overlay-shadow ui-header ui-bar-d" role="banner">
		<h1 class="ui-title" tabindex="0" role="heading" aria-level="1">${title}</h1>
	</div>
	<div data-role="content" data-theme="c"	class="ui-overlay-shadow ui-corner-bottom ui-content ui-body-c dialogFixes" role="main">
		<p>
			<center>
				<br>
				<span class="locationDetailsFont">
					{{html message}}
				</span>
				<br>
				<br>
			</center>
		</p>
		<div class="dialogButtonContainer">
			<a href="#" id="confirm_confirmButton" data-rel="back" data-role="button" data-theme="c">${yesButton}</a>
			<a href="#" id="confirm_cancelButton" data-rel="back" data-role="button" data-theme="c">${noButton}</a>
		</div>
	</div>
</div>
</script>
<script id="homePhoneConfirmMessageDialog-template" type="text/x-jquery-tmpl">
<div data-role="dialog" id="messageDialog" class=".ui-page_homePhone ui-body-c ui-overlay-a ui-dialog_homePhone ui-page-active" role="dialog">
	<div data-role="header" data-theme="d" class="ui-corner-top ui-overlay-shadow ui-header ui-bar-d" role="banner">
		<h1 class="ui-title" tabindex="0" role="heading" aria-level="1">${title}</h1>
	</div>
	<div data-role="content" data-theme="c"	class="ui-overlay-shadow ui-corner-bottom ui-content ui-body-c dialogFixes" role="main">
		<p>
			<center>
				
				   <h3>${mainLabel_one} <br>
				       ${mainLabel_two}
				   </h3>
			       <!-- <h3>${mainLabel_two}</h3> -->
				<!-- <span class="locationDetailsFont">
					<table class="homePhoneTitleTable">
					<tr>
					<td width="40%">
					    <label  for="homephoneVerify">Home phone </label>
					</td>
					<td width="60%">    
     					<input size="10" type="tel" id="homePhone" tabindex="1" maxlength="10" />
     				</td>	
     				</tr>
     				</table>
				</span>  -->
				<span class="locationDetailsFont">
				 <table class="homePhoneTable">
					<tr>
					<td width="30%">
     					<input size="3" class="homePhoneInput" type="tel" id="homePhone1" tabindex="1" maxlength="3" >
     				</td>	
     				<td width="30%">
     				    <input size="3" class="homePhoneInput" type="tel" id="homePhone2" tabindex="2" maxlength="3" >
     				</td>
     			    <td width="40%">
     			        <input size="4" class="homePhoneInputLast" type="tel" id="homePhone3" tabindex="3" maxlength="4" >
     			    </td>   
     				
     				</tr>
     				</table>
				</span>
	
				
			</center>
		</p>
		<div class="dialogButtonContainer">
			<a href="#" id="confirm_confirmButton" data-rel="back" data-role="button" data-theme="c">${yesButton}</a>
		</div>
	</div>
</div>
</script>
<script id="legalHandoutAttestationSignatureDialog-template" type="text/x-jquery-tmpl">
<div data-role="dialog" id="messageDialog" class="ui-page ui-body-c ui-overlay-a ui-dialog ui-page-active" role="dialog">
	<div data-role="header" data-theme="d" class="ui-corner-top ui-overlay-shadow ui-header ui-bar-d" role="banner">
		<h1 class="ui-title" tabindex="0" role="heading" aria-level="1">${title}</h1>
	</div>
	<div data-role="content" data-theme="c"	class="ui-overlay-shadow ui-corner-bottom ui-content ui-body-c dialogFixes" role="main">
		<p>
			<center>
				<br>
				<span class="locationDetailsFont">
					{{html message}}
				</span>
				<br>
				<br>
			</center>
		</p>
        <center> 
                <span class="buttonTextPleasesign">${pleasesign_text}</span>
                 <br>
                 <br>
            	<div class="signatureContainer sugnatureFixesLegalHandout" >
                	<div class="signatureBorder" id="signatureScreen_SingnatureContainer">
                    	<div class="signature" id="signatureLegalHandOut"/>
                    </div>
                </div>
                <br>
                <br>
                <span id="signature_Reset_Button_legalHandout" class="button grayflat sigResetButton proceedButtonWidth">
                 <!-- signatureScreen_Reset_Button_Label -->
                <span class="buttonText">${clearsignature_text}</span>
                </span>
        </center>
        <br>
		<div id="YesNoButtonContainer" class="dialogButtonContainer">
			<a href="#" id="confirm_confirmButton" data-rel="back" data-role="button" data-theme="c">${yesButton}</a>
			<a href="#" id="confirm_cancelButton" data-rel="back" data-role="button" data-theme="c">${noButton}</a>
		</div>
	</div>
</div>
</script>
<script id="messageDialog-template" type="text/x-jquery-tmpl">
<div data-role="dialog" id="${messageDialogId}">	
	<div data-role="header" data-theme="d" class="ui-corner-top ui-overlay-shadow ui-header ui-bar-d" role="banner">
		<h1 class="ui-title" tabindex="0" role="heading" aria-level="1">${title}</h1>
	</div>
	<div data-role="content" data-theme="c" class="ui-overlay-shadow ui-corner-bottom ui-content ui-body-c dialogFixes" role="main">
		{{if _.isArray(message)}}
			{{if itemizedList}}
				<ol>
			{{else}}
				<ul class="multiInfoMessageContainer">
			{{/if}}
			    {{each(i, aMessage) message}}
			        <li class="multiInfoMessage locationDetailsFont ${liClass}">{{html aMessage}}</li>
				{{/each}}
			{{if itemizedList}}
				</ol>
			{{else}}
				</ul>
			{{/if}}
		{{else}}
			<p>
				<center>
				<br>
				<span class="locationDetailsFont">
					{{html message}}
				</span>
				<br>
				<br>
				</center>
			</p>
		{{/if}}
		<div class="dialogButtonContainer">
			<a href="#" data-rel="back" id="messageDialogOkay" data-role="button" data-theme="c" class="ui-btn ui-shadow ui-btn-corner-all ui-btn-up-c">
				<span class="ui-btn-inner ui-btn-corner-all" aria-hidden="true">
					<span class="ui-btn-text">${button}</span> 
				</span> 
			</a>
		</div>
	</div>
</div>
</script>
<script id="optionalProductDialog" type="text/x-jquery-tmpl">
<div data-role="dialog" id="messageDialog" >	
	<div id="optionalHeader" data-role="header" class="optionalDialogWidth" data-theme="d" class="ui-corner-top ui-overlay-shadow ui-header ui-bar-d" role="banner">
		<h1 class="ui-title" tabindex="0" role="heading" aria-level="1">${title}</h1>
	</div>
	<div id="optionalcontent" data-role="content" data-theme="c" class="ui-overlay-shadow ui-corner-bottom ui-content ui-body-c dialogFixes" role="main">			
			<span class="OptionalDialogFont">
				{{html message}}
			</span>			
		<div class="dialogButtonContainer">
			<a href="#" data-rel="back" id="messageDialogOkay" data-role="button" data-theme="c" class="ui-btn ui-shadow ui-btn-corner-all ui-btn-up-c">
				<span class="ui-btn-inner ui-btn-corner-all" aria-hidden="true">
					<span class="ui-btn-text">${button}</span> 
				</span> 
			</a>
		</div>
	</div>
</div>
</script>
<script id="printerErrorMessageDialog-template" type="text/x-jquery-tmpl">
<div data-role="dialog" id="${messageDialogId}">	
	<div data-role="header" data-theme="d" class="ui-corner-top ui-overlay-shadow ui-header ui-bar-d" role="banner">
		<h1 class="ui-title" tabindex="0" role="heading" aria-level="1">${title}</h1>
	</div>
	<div data-role="content" data-theme="c" class="ui-overlay-shadow ui-corner-bottom ui-content ui-body-c dialogFixes" role="main">
		{{if _.isArray(errorMessage)}}
			{{if itemizedList}}
				<ol>
			{{else}}
				<ul class="multiInfoMessageContainer">
			{{/if}}
			    {{each(i, aMessage) errorMessage}}
			        <li class="multiInfoMessage printerErrorDetailsFont ${liClass}">{{html aMessage}}</li>
				{{/each}}
			{{if itemizedList}}
				</ol>
			{{else}}
				</ul>
			{{/if}}
		{{else}}
			<p>
				<center>
				<br>
				<span class="printerErrorDetailsFont">
					{{html errorMessage}}
				</span>
				<br>
				<br>
				</center>
			</p>
		{{/if}}		
		{{if _.isArray(message)}}
			{{if itemizedList}}
				<ol>
			{{else}}
				<ul class="multiInfoMessageContainer">
			{{/if}}
			    {{each(i, aMessage) message}}
			        <li class="multiInfoMessage printerErrorDetailsFont ${liClass}">{{html aMessage}}</li>
				{{/each}}
			{{if itemizedList}}
				</ol>
			{{else}}
				</ul>
			{{/if}}
		{{else}}
			<p>
				<center>
				<br>
				<span class="printerErrorDetailsFont">
					{{html message}}
				</span>
				<br>
				<br>
				</center>
			</p>
		{{/if}}
		<div class="dialogButtonContainer">
			<a href="#" data-rel="back" id="messageDialogOkay" data-role="button" data-theme="c" class="ui-btn ui-shadow ui-btn-corner-all ui-btn-up-c">
				<span class="ui-btn-inner ui-btn-corner-all" aria-hidden="true">
					<span class="ui-btn-text">${button}</span> 
				</span> 
			</a>
		</div>
	</div>
</div>
</script>
<script id="printerSetupDialog-template" type="text/x-jquery-tmpl">
<div data-role="dialog" id="messageDialog" >
	<div data-role="header" data-theme="d" class="ui-corner-top ui-overlay-shadow ui-header ui-bar-d" role="banner">
		<h1 class="ui-title" tabindex="0" role="heading" aria-level="1">${title}</h1>
	</div>
	<div data-role="content" data-theme="c"	class="ui-overlay-shadow ui-corner-bottom ui-content ui-body-c dialogFixes" role="main">
		<p>
			<center>
			    <h3>${mainLabel}</h3>
			    <span>${ieLabel}</span>
                <pre style="display: inline"> ac : 3f : a4 : 10 : 84 : d8</pre>
				<br/>
				<br/>
				<span class="locationDetailsFont">
					<form>
     					<input class="macAddressFirstInput macAddressInput" type="text" id="printerMacAddress1" tabindex="1" maxlength="2">
     					<label class="macAddressLabel" for="printerMacAddress2">:</label>
     					<input class="macAddressInput" type="text" id="printerMacAddress2" tabindex="2" maxlength="2">
     					<label class="macAddressLabel" for="printerMacAddress3">:</label>
     					<input class="macAddressInput" type="text" id="printerMacAddress3" tabindex="3" maxlength="2">
     					<label class="macAddressLabel" for="printerMacAddress4">:</label>
     					<input class="macAddressInput" type="text" id="printerMacAddress4" tabindex="4" maxlength="2">
     					<label class="macAddressLabel" for="printerMacAddress5">:</label>
     					<input class="macAddressInput" type="text" id="printerMacAddress5" tabindex="5" maxlength="2">
     					<label class="macAddressLabel" for="printerMacAddress6">:</label>
     					<input class="macAddressInput" type="text" id="printerMacAddress6" tabindex="6" maxlength="2">
     				</form>
				</span>
				<br>
				<br>
			</center>
		</p>
		<div class="dialogButtonContainer">
			<a href="#" id="confirm_confirmButton" data-rel="back" data-role="button" data-theme="c">${yesButton}</a>
			<a href="#" id="confirm_cancelButton" data-rel="back" data-role="button" data-theme="c">${noButton}</a>
		</div>
	</div>
</div>
</script>

<script id="QCDistributionGuideMessageDialog-template" type="text/x-jquery-tmpl">

<div data-role="dialog" id="messageDialog">
<div class="QccancelDialog">
	<div data-role="header" data-theme="d" class="ui-corner-top ui-overlay-shadow ui-header ui-bar-d" role="banner">
		<center>
          <h1 class="ui-title" tabindex="0" role="heading" aria-level="1">${title}</h1>
        </center>
	</div>
	<div data-role="content" data-theme="c"	class="ui-overlay-shadow ui-corner-bottom ui-content ui-body-c dialogFixes" role="main">
		<p>
			<center>
				<span class="locationDetailsFont">
					${message}
				</span>
				<br>
				<br>
			</center>
		<div class="dialogButtonContainer">
			<a href="#" id="confirm_confirmButton" data-rel="back" data-role="button" data-theme="c">${yesButton}</a>
			<a href="#" id="confirm_cancelButton" data-rel="back" data-role="button" data-theme="c">${noButton}</a>
		</div>
	</div>
</div>
</div>
</script>
<script id="removeExpiredActivationsDialog-template" type="text/x-jquery-tmpl">
<div data-role="dialog" id="messageDialog" class="removeExpired">	
	<div data-role="header" data-theme="d" class="ui-corner-top ui-overlay-shadow ui-header ui-bar-d" role="banner">
		<h1 class="ui-title" tabindex="0" role="heading" aria-level="1">${title}</h1>
	</div>
	<div data-role="content" data-theme="c" class="ui-overlay-shadow ui-corner-bottom ui-content ui-body-c dialogFixes" role="main">
		<p>${message.preList}</p>
		<ul>
		    {{each(i, aMessage) message.list}}
		        <li class="multiInfoMessage locationDetailsFont ${liClass}">{{html aMessage}}</li>
			{{/each}}
		</ul>
		<p>${message.postList}</p>
		<div class="dialogButtonContainer">
			<a href="#" data-rel="back" data-role="button" data-theme="c" class="ui-btn ui-shadow ui-btn-corner-all ui-btn-up-c">
				<span class="ui-btn-inner ui-btn-corner-all" aria-hidden="true"> 
					<span class="ui-btn-text">${button}</span> 
				</span> 
			</a>
		</div>
	</div>
</div>
</script>
<script id="ScanDialog-template" type="text/x-jquery-tmpl">
<div data-role="dialog" id="messageDialog" >
	<div data-role="header" data-theme="d" class="ui-corner-top ui-overlay-shadow ui-header ui-bar-d" role="banner">
		<h1 class="ui-title" tabindex="0" role="heading" aria-level="1">${title}</h1>
	</div>
	<div data-role="content" data-theme="c"	class="ui-overlay-shadow ui-corner-bottom ui-content ui-body-c dialogFixes" role="main">
		<p>
			<center>
			    <img src="app/images/scan_image.png"/>
			</center>
		</p>
		<p>${holdText}</p>
		<p>${pressText}</p>
		<div class="dialogButtonContainer">
			<a href="#" id="confirm_confirmButton" data-rel="back" data-role="button" data-theme="c">${yesButton}</a>
			<a href="#" id="confirm_cancelButton" data-rel="back" data-role="button" data-theme="c">${noButton}</a>
		</div>
	</div>
</div>
</script>

<script id="scanFailedErrorMessageDialog-template" type="text/x-jquery-tmpl">
<div data-role="dialog" id="messageDialog" >
	<div data-role="header" data-theme="d" class="ui-corner-top ui-overlay-shadow ui-header ui-bar-d" role="banner">
		<h1 class="ui-title" tabindex="0" role="heading" aria-level="1">${scanFiledtitle}</h1>
	</div>
	<div data-role="content" data-theme="c"	class="ui-overlay-shadow ui-corner-bottom ui-content ui-body-c dialogFixes" role="main">
		<center>
		        <p class="scannedFailedErrorFont"><b>${scanFailedMessage1}</b></p>
		</center>
		<div class="dialogButtonContainer">
			<a href="#" id="scan_failed_error_message_OK_button" data-rel="back" data-role="button" data-theme="c">${scanFailedYesButton}</a>
		</div>
	</div>
</div>
</script>

<script id="ScanLoyaltyDialog-template" type="text/x-jquery-tmpl">
<div data-role="dialog" id="messageDialog" >
	<div data-role="header" data-theme="d" class="ui-corner-top ui-overlay-shadow ui-header ui-bar-d" role="banner">
		<h1 class="ui-title" tabindex="0" role="heading" aria-level="1">${title}</h1>
	</div>
	<div data-role="content" data-theme="c"	class="ui-overlay-shadow ui-corner-bottom ui-content ui-body-c dialogFixes" role="main">
		<p>
			<center>
			    <img src="app/images/scan_image.png"/>
			</center>
		</p>
		<p>${holdText}</p>
		<p>${pressText}</p>
		<div class="dialogButtonContainer">
			<a href="#" id="confirm_confirmButton" data-rel="back" data-role="button" data-theme="c">${yesButton}</a>
			<a href="#" id="confirm_cancelButton" data-rel="back" data-role="button" data-theme="c">${noButton}</a>
		</div>
	</div>
</div>
</script>

<script id="screenDialog-template" type="text/x-jquery-tmpl">
<div data-role="dialog" id="screenDialog" >
	<div data-role="header" data-theme="d" class="ui-corner-top ui-overlay-shadow ui-header ui-bar-d" role="banner">
		<h1 class="ui-title" tabindex="0" role="heading" aria-level="1">${title}</h1>
	</div>
	<div data-role="content" data-theme="c"	class="ui-overlay-shadow ui-corner-bottom ui-content ui-body-c dialogFixes" role="main">
		
		<p>${message}</p>
		<div class="dialogButtonContainer">
			<a href="#" id="confirm_confirmButton" data-rel="back" data-role="button" data-theme="c">${yesButton}</a>
			<a href="#" id="confirm_cancelButton" data-rel="back" data-role="button" data-theme="c">${noButton}</a>
		</div>
	</div>
</div>
</script>
<script id="settingsDialog-template" type="text/x-jquery-tmpl">
<div data-role="dialog" id="messageDialog">
	<div data-role="header" data-theme="d" class="ui-corner-top ui-overlay-shadow ui-header ui-bar-d" role="banner">
		<h1 class="ui-title" tabindex="0" role="heading" aria-level="1">${title}</h1>
	</div>
	<div data-role="content" data-theme="c"	class="ui-overlay-shadow ui-corner-bottom ui-content ui-body-c dialogFixes" role="main">
		<p>${message}</p>
		<div class="dialogButtonContainer">
			<a href="#" id="settings_logOutButton" data-rel="back" data-role="button" data-theme="c">${logOutButton}</a>
			<a href="#" id="settings_chooseProductButton" data-rel="back" data-role="button" data-theme="c">${chooseProductButton}</a>
			<a href="#" id="settings_printerSetupButton" data-rel="back" data-role="button" data-theme="c">${printerSetupButton}</a>
			<a href="#" id="settings_testPrintButton" data-rel="back" data-role="button" data-theme="c">${testPrintButton}</a>
			{{if retrieveButtonDisplayed}}
				<a href="#" id="settings_retrieveButton" data-rel="back" data-role="button" data-theme="c">${retrieveButton}</a>
			{{/if}}
			<a href="#" id="settings_toggleLanguageButton" data-rel="back" data-role="button" data-theme="c">${toggleLanguageButton}</a>
			
			<a href="#" id="settings_manageRepsButton" data-rel="back" data-role="button" data-theme="c">${manageRepsButton}</a>
			
			<a href="#" id="cancel" data-rel="back" data-role="button" data-theme="c">${chancelButton}</a>
		</div>
	</div>
</div>
</script>

<script id="signatureDialog-template" type="text/x-jquery-tmpl">
<div data-role="dialog" id="messageDialog" >
	<div data-role="header" data-theme="d" class="ui-corner-top ui-overlay-shadow ui-header ui-bar-d" role="banner">
		<h1 class="ui-title" tabindex="0" role="heading" aria-level="1">${title}</h1>
	</div>
	<div data-role="content" data-theme="c"	class="ui-overlay-shadow ui-corner-bottom ui-content ui-body-c dialogFixes" role="main">
		<p>
			<center>
				<br>
				<span class="locationDetailsFont">
					${message}
				</span>
				<br>
				<br>
			</center>
		</p>
		<div class="dialogButtonContainer">
			<a href="#" id="signature_resetButton" data-rel="back" data-role="button" data-theme="c">${resetButton}</a>
			<a href="#" id="signature_cancelButton" data-rel="back" data-role="button" data-theme="c">${canceButton}</a>
			<a href="#" id="signature_okButton" data-rel="back" data-role="button" data-theme="c">${okButton}</a>			
		</div>
		<br>
		<div class="signatureBorder" >				
			<div class="signature" id="signature"/>															
		</div>
		<br>
	</div>
</div>
</script>

<script id="CC_Legal_COCD-template" type="text/x-jquery-tmpl"> 
<br/>
<h3 data-translationKey="overview_CostOfCreditDisclosure_MainTitle" style="padding: 0 0 10px 10px;"></h3>
<table class="overviewCenterTableContainer cocd_table">
    <tr>
        <td style="border: none;">
            <table class="overviewCenterTable">
                    <tr>
                    <th colspan="2" id="overviewCenterTableTitlePadding" data-translationKey="overview_CostOfCreditDisclosure_Title"></th>
                </tr>
                    <tr>
                    <td class="overviewCenterTableTitle" data-translationKey="overview_CostOfCreditDisclosure_Left1"></td>
                    <td class="overviewCenterTableData">
						{{if activationItems.getModel('chooseProductModel').get('productCard') == "OMX"}}
						<p id="overviewCenterTableTextWithoutMargin" data-translationKey="overview_CostOfCreditDisclosure_ChooseProduct_Right1_OMX"></p>
						{{else}}
                        <p id="overviewCenterTableTextWithoutMargin" data-translationKey="overview_CostOfCreditDisclosure_ChooseProduct_Right1"></p>
						{{/if}}
                    </td>
                    </tr>
                    <tr>
                    <td class="overviewCenterTableTitle" data-translationKey="overview_CostOfCreditDisclosure_Left2"></td>
                    <td class="overviewCenterTableData">
                        <p id="overviewCenterTableTextWithoutMargin" data-translationKey="overview_CostOfCreditDisclosure_Right2"></p>
                    </td>
                    </tr>
                    <tr>
                    <td class="overviewCenterTableTitle" data-translationKey="overview_CostOfCreditDisclosure_Left3"></td>
                    <td class="overviewCenterTableData">
                        <p id="overviewCenterTableTextWithoutMargin" data-translationKey="overview_CostOfCreditDisclosure_Right3"></p>
                    </td>
                    </tr>
                    <tr>
                    <td class="overviewCenterTableTitle" data-translationKey="overview_CostOfCreditDisclosure_Left4"></td>
                    <td class="overviewCenterTableData">
                        <p id="overviewCenterTableTextWithoutMargin" data-translationKey="overview_CostOfCreditDisclosure_Right4"></p>
                    </td>
                    </tr>
                    <tr>
                    <td class="overviewCenterTableTitle" data-translationKey="overview_CostOfCreditDisclosure_Left5"></td>
                    <td class="overviewCenterTableData">
                        <p id="overviewCenterTableTextWithoutMargin" data-translationKey="overview_CostOfCreditDisclosure_Right5"></p>
                    </td>
                    </tr>
                    <tr>
                    <td class="overviewCenterTableTitle" style="border-bottom: none !important;" data-translationKey="overview_CostOfCreditDisclosure_Left6"></td>
                    <td class="overviewCenterTableData" style="border-bottom: none !important;">
                        <p id="overviewCenterTableTextWithoutMargin" data-translationKey="overview_CostOfCreditDisclosure_Right6"></p>
                    </td>
                    </tr>
            </table>
        </td>
    </tr>
</table>

<div class="overViewInterest">
<span data-translationKey="overview_AccrualInterest"></span>
<br><br>
</div>


<div class="overViewInterest">
<span data-translationKey="overview_InterestRates"></span>
</div>

<!-- US3381 -->
<div class="overviewCenterTableContainer elementWithLeft10 bottomSpacing elementWithFontSize10" data-translationKey="overview_EffectiveDate"></div>

</script>

<script id="CC_Legal_OMC-template" type="text/x-jquery-tmpl">
<span id="legalOMC" class="TandC_Content_Format">
<!--<br><span data-translationKey="legal_omc_seventh_chapter_ectm" ></span>-->
<br><span data-translationKey="legal_omc_first_chapter_ectm" ></span>
<br><span data-translationKey="legal_omc_fourth_chapter_ectm" ></span>
<!-- US3766
<br><span data-translationKey="legal_omc_third_chapter" ></span>
//-->
<br><span data-translationKey="legal_omc_fives_chapter" ></span>
<br><span data-translationKey="legal_omc_sixth_chapter" ></span>
</span>
</script>

<script id="CC_Legal_OMP-template" type="text/x-jquery-tmpl">
<!--<span id="TandCTitle" class="TandC_Title_Format" data-translationKey="chooseProduct_ReadTandC"></span>//-->
<span id="legalOMP" class="TandC_Content_Format">
<br>*<span data-translationKey="legal_omp_first_chapter"></span>
<br><span data-translationKey="legal_omp_second_chapter"></span>
<br><sup>&dagger;</sup><span data-translationKey="legal_omp_third_chapter"></span>
<br><sup>&dagger;&dagger;</sup><span data-translationKey="legal_omp_fourth_chapter"></span>
<br><sup>1</sup><span data-translationKey="legal_omp_fives_chapter"></span>
<!-- US3997 //-->
<!-- <br><sup>2</sup><span data-translationKey="legal_omp_six_chapter"></span> //-->
<br><span data-translationKey="legal_omp_six_chapter"></span>
<br>
<br><span data-translationKey="legal_omp_eight_chapter"></span>
<br><span data-translationKey="legal_omp_nine_chapter"></span>
<span>
</script>
<script id="CC_Legal_OMR-template" type="text/x-jquery-tmpl">
<!--<span id="TandCTitle" class="TandC_Title_Format" data-translationKey="chooseProduct_ReadTandC"></span>//-->	
<span id="legalOMR" class="TandC_Content_Format">
<!-- US3766 //-->
<br><span data-translationKey="legal_omr_first_chapter" ></span>
<br><span data-translationKey="legal_omr_second_chapter" ></span>
<br><span data-translationKey="legal_omr_third_chapter" ></span>
<br><span data-translationKey="legal_omr_four_chapter" ></span>
<br><span data-translationKey="legal_omr_five_chapter" ></span>
<br><br><span data-translationKey="legal_omr_six_chapter" ></span>
<br><br><span data-translationKey="legal_omr_seven_chapter" ></span>
<br><span data-translationKey="legal_omr_eight_chapter" ></span>
<br><span data-translationKey="legal_omr_nine_chapter" ></span>
</span> 
</script>
<script id="NavBar-template" type="text/x-jquery-tmpl">
	<img id="navBarImage"></img>
</script>
<script id="CC_OMC-template" type="text/x-jquery-tmpl"> 
    <blockquote>    
      <center>
        <a href="#" id="omxCardChooseProduct" class="omxCardChooseProduct"></a>
      </center>       
	</blockquote>
</script>

<script id="CC_OMP-template" type="text/x-jquery-tmpl">
    <blockquote>
        <div class="copy">
            <p>  
                <b><span data-translationKey="omp_1_chapter" style="font-size: medium;"></span></b>
            </p>
            <p><span data-translationKey="omp_2_chapter"></span></p>
        </div>
        <!--<img src="app/images/e_gasadvan_image_MR.gif" alt="" class="contentImgRight" />-->
        <div class="copy">
            <p>
                <b><span data-translationKey="omp_3_chapter"></span></b>
            </p>
            <p><span data-translationKey="omp_4_chapter"></span>
		<br/>
		<br><sup>&dagger;</sup><span data-translationKey="omp_5_chapter"></span>
            </p>            
            <p>
                <b><span data-translationKey="omp_6_chapter"></span></b>
            </p>
            <ul>
                <li><span data-translationKey="omp_7_chapter"></span>
                </li>
                <li><span data-translationKey="omp_8_chapter"></span>
                </li>
                <li><span data-translationKey="omp_9_chapter"></span>
                    </li>
                <li><span data-translationKey="omp_10_chapter"></span>
                </li>
                <li><span data-translationKey="omp_11_chapter"></span>
                </li>
                <li><span data-translationKey="omp_12_chapter"></span>
                </li>
                <li><span data-translationKey="omp_13_chapter"></span>
                </li>
            </ul>
            <p><span data-translationKey="omp_14_chapter"></span>
            </p>
            <ul>
                <li><span data-translationKey="omp_15_chapter"></span>
                </li>
            </ul>
        </div>
    </blockquote>
</script>

<script id="CC_OMR-template" type="text/x-jquery-tmpl">
    <blockquote>
        <div class="copy">
            <p>
                <b><span data-translationKey="omr_1_chapter"></span></b>
            </p>
            <!-- US3766 //-->
            <p>
            	<span data-translationKey="omr_1_1_chapter"></span>
            </p>
            <p>
            	<span data-translationKey="omr_1_2_chapter"></span>
            </p>
            <ul>
                <li>
                    <div><span data-translationKey="omr_2_chapter"></span>
                    </div>
                </li>
                <li>
                    <div><span data-translationKey="omr_3_chapter"></span>
                    </div>
                </li>
                <li>
                    <div><span data-translationKey="omr_4_chapter"></span>
                    </div>
                </li>
                <li>
                    <div><span data-translationKey="omr_5_chapter"></span>
                    </div>
                </li>
            </ul>
            <p>
                <b><span data-translationKey="omr_6_chapter"></span></b>
            </p>
            <ul>
            <!-- US3766 //-->
                <li><span data-translationKey="omr_7_chapter"></span>
                </li>
                <li><span data-translationKey="omr_8_chapter"></span>
                </li>
                <li><span data-translationKey="omr_9_chapter"></span>
                    </li>
                <li><span data-translationKey="omr_10_chapter"></span>
                </li>
                <li><span data-translationKey="omr_11_chapter"></span>
                </li>
                <li><span data-translationKey="omr_12_chapter"></span>
                </li>
                <li><span data-translationKey="omr_13_chapter"></span>
                </li>
            </ul>
            <p><span data-translationKey="omr_14_chapter"></span>
            </p>
            <ul>
                <li><span data-translationKey="omr_15_chapter"></span>
                </li>
            </ul>
        </div>
    </blockquote>
</script>

<script id="WICIBreadcrumbs" type="text/x-jquery-tmpl">
    <ul id="BreadcrumbTrailArea" class="breadcrumbs {{if isNotEmployee}} breadcrumbs-longer {{if current === 6 || current === 8 }} widthOptionalProductBreadcrum {{/if}} {{if current === 8 }} breadcrumbs-full {{/if}}  {{/if}} {{if !isNotEmployee}} {{if current === 8 }} breadcrumbs-full{{/if}} {{/if}} ScreenLayoutFullWidth">
        <li class="{{if current === 1}}breadcrumbs-item-current{{else current > 1}}breadcrumbs-item-active{{/if}}">
            <span class="breadcrumbs-item-text-wrapper">1. <span data-translationKey="breadCrumbItem_ProductSelection"></span>
            </span>
        </li>
        <li class="{{if current === 2}}breadcrumbs-item-current{{else current > 2}}breadcrumbs-item-active{{/if}}">
            <span class="breadcrumbs-item-text-wrapper">2. <span data-translationKey="breadCrumbItem_EmailInfo"></span>
            </span>
        </li>
        <li class="{{if current === 3}}breadcrumbs-item-current{{else current > 3}}breadcrumbs-item-active{{/if}}">
            <span class="breadcrumbs-item-text-wrapper">3. <span data-translationKey="breadCrumbItem_ApplicantInfo"></span>
            </span>
        </li>        
        <li class="{{if current === 4}}breadcrumbs-item-current{{else current > 4}}breadcrumbs-item-active{{/if}}">
            <span class="breadcrumbs-item-text-wrapper">4. <span data-translationKey="breadCrumbItem_FinancialAndEmploymentInfo"></span>
            </span>
        </li>
        <li class="{{if current === 5}}breadcrumbs-item-current{{else current > 5}}breadcrumbs-item-active{{/if}}">
            <span class="breadcrumbs-item-text-wrapper">5. <span data-translationKey="breadCrumbItem_SupplementaryCard"></span>
            </span>
        </li>
        {{if isNotEmployee}}
        <li class="{{if current === 6}}breadcrumbs-item-current{{else current > 6}}breadcrumbs-item-active{{/if}}">
            <span class="breadcrumbs-item-text-wrapper">6. <span data-translationKey="breadCrumbItem_OptionalProducts"></span>
            </span>
        </li>
        {{/if}}
        {{if isNotEmployee && !landLineFlag}}
        <li class="{{if current === 7}}breadcrumbs-item-current{{else current > 7}}breadcrumbs-item-active{{/if}}">
            <span class="breadcrumbs-item-text-wrapper">7. <span data-translationKey="breadCrumbItem_MobilePayments"></span>
            </span>
        </li>        
        {{else !landLineFlag}}
        <li class="{{if current === 7}}breadcrumbs-item-current{{else current > 7}}breadcrumbs-item-active{{/if}}">
            <span class="breadcrumbs-item-text-wrapper">6. <span data-translationKey="breadCrumbItem_MobilePayments"></span>
            </span>
        </li>
        {{/if}}
        <li class="{{if current === 8}}breadcrumbs-item-current{{/if}}">
            <span class="breadcrumbs-item-text-wrapper"><span data-translationKey="breadCrumbItem_Confirmation"></span>
            </span>
        </li>
    </ul>
</script>

<script id="MobilePaymentNotePageFooter-template" type="text/x-jquery-tmpl">
	<footer class="pageFooter disclaimerFooterHeight">
		<div class="disclaimerFooterText">
		      <p class="personalInfoIncText" data-translationKey="personalData_TermsandConditions_Para1"></p>
          	  <p class="personalInfoIncText" data-translationKey="personalData_TermsandConditions_Para2"></p>
          	  <br>
	          <span class="phoneNote" data-translationKey="personalData_Note"></span>
		</div>
	</footer>
	<br>
</script>

<script id="pageFooter-template" type="text/x-jquery-tmpl">
	<footer class="pageFooter darkGrayGradient">
		{{if previousButtonId}}
			<a href="#" data-theme="r" data-role="button" class=" ${previousButtonId} pageNavigationButton previous ui-btn ui-btn-icon-left ui-corner-left ui-btn-up-r">
				<span class="buttonAltText"></span>
				<span class="buttonAltTextLabel" data-translationKey="pageHeader_previous"></span>
			</a>
		{{/if}}

		{{if nextButtonId}}
			<a href="#" data-theme="r" data-role="button" class="${nextButtonId} pageNavigationButton next ui-btn ui-btn-icon-right ui-corner-right ui-btn-up-r">
				<span class="buttonAltTextLabel" data-translationKey="pageHeader_next"></span>
				<span class="buttonAltText"></span>
			</a>
		{{/if}}
	</footer>
</script>

<script id="pageHeader-template" type="text/x-jquery-tmpl">
	<header class="pageHeader darkGrayGradient">
            {{if logo_En}}
                <div class="PageHeader_CanadianTireLogo PageHeader_CanadianTireLogo_English {{if previousButtonId}} PageHeader_CanadianTireLogo_LeftPadding {{/if}} {{if nextButtonId}} PageHeader_CanadianTireLogo_RightPadding {{/if}}"><div class="PageHeader_CanadianTireLogo_inner"></div></div></img>
            {{else}}
                <div class="PageHeader_CanadianTireLogo PageHeader_CanadianTireLogo_French {{if previousButtonId}} PageHeader_CanadianTireLogo_LeftPadding {{/if}} {{if nextButtonId}} PageHeader_CanadianTireLogo_RightPadding {{/if}}"><div class="PageHeader_CanadianTireLogo_inner"></div></div></img>
            {{/if}}

		{{if previousButtonId}}
			<a href="#" data-theme="r" data-role="button" class="${previousButtonId} pageNavigationButton previous ui-btn ui-btn-icon-left ui-corner-left ui-btn-up-r">
				<span class="buttonAltText"></span>
				<span class="buttonAltTextLabel" data-translationKey="pageHeader_previous"></span>
			</a>
		{{/if}}

		<div class="pageHeader-right-button-group">
			{{if settingsButtonId}}
				<a href="##uploadOptions" data-theme="r" data-role="button" id="${settingsButtonId}" class="pageNavigationButton settings ui-btn ui-btn-icon-right ui-corner-right ui-btn-up-r"
					data-rel="dialog" data-transition="slidedown">
					<!--<span class="buttonAltText" data-translationKey="settings"></span>-->
					<span class="buttonAltText"></span>
				</a>
			{{/if}}

			{{if nextButtonId}}
				<a href="#" data-theme="r" data-role="button" class="${nextButtonId} pageNavigationButton next ui-btn ui-btn-icon-right ui-corner-right ui-btn-up-r">
					<span class="buttonAltTextLabel" data-translationKey="pageHeader_next"></span>
					<span class="buttonAltText"></span>
				</a>
			{{/if}}
		</div>
	</header>
</script>

<script id="pageHeaderTrainingModule-template" type="text/x-jquery-tmpl">
	<header class="pageHeader">           
		{{if previousButtonId}}
			<a href="#" data-theme="r" data-role="button" class="${previousButtonId} pageNavigationButton previous ui-btn ui-btn-icon-left ui-corner-left ui-btn-up-r">
				<span class="buttonAltText"></span>
				<!--<span class="buttonAltTextLabel" data-translationKey="pageHeader_previous"></span> -->
			</a>
		{{/if}}

		<div class="pageHeader-right-button-group">
            {{if languageButtonId}}
			<div class="flipAlignRight marginForLanguage">
            	<select  data-role="slider" id="${languageButtonId}">
                	<option value="E" selected>En</option>
                	<option value="F">Fr</option>
            	</select>
        	</div>
          {{/if}}
		</div>
	</header>
</script>

<script id="WICIAgentAttributionScreen-template" type="text/x-jquery-tmpl">
    <div class="ScreenLayout wiciLoginPage" id="agentAttributionPage_PageContents">
        <br>
        <br>
        <br>
       
        <center>
            <div id="CTFSLogo" class="Login_CanadianTireLogo"></div>
        </center>
        <br>
        <br>
        <br>
        <div id="loginScreen_FieldsArea">
            <center>
                <div class="wrapLoginTable">
                    <table id="loginFieldsTable" class="agentTableWidth">
                        <tr class="fieldRow">
                            <td class="fieldEmailInfoCell fieldSize55">
                                <!--<span data-translationKey="loginScreen_UserID_Label"></span>//-->
                                <span data-translationKey="agentAttribution_EnterAgentId_label"></span>
                            </td>
                            <td class="fieldValuesTopCell">                                
                                <input class="fieldValuesTextField upperCaseField" id="agentAttributionScreen_agentIDTextField" type="text" maxlength="7" tabindex=1/>
                            </td>
                        </tr>
                        <tr class="fieldRow hidden">
                            <td class="fieldLabelsBottomCell fieldSize55">
                                <span data-translationKey="agentAttribution_NewPassword_label"></span>
                            </td>
                            <td class="fieldValuesBottomCell">
                                <input class="fieldValuesTextField upperCaseField" id="newPasswordField" maxlength="6" type="text" tabindex=2/>
                            </td>
                        </tr>
                    </table>
                </div>
            </center>
            <br>
            <br>
            <br>
            <br>
            <br>
            <br>
            <br>
            <br>
            <center>
          
          <div class="wrapLoginTable">
            <table class="radioButtonTableNoBorder">
                        <tr>
                            <td class="radioButtonWidth">
                            
                                    <div class="agentRadiobutton">
                                         <input type="radio" id="create_CheckField" name="radios" value="Create" checked>
                                         <!--<label for="create">Create</label> -->

                                    </div> 
                                  
                            </td>
                            <td class="radioTextWidth">
                                 <span class="agentRadioButton" data-translationKey="agentLoginScreen_Create"></span>
                            </td>
                            
                            <td class="radioButtonWidth">
                                       <div class="agentRadiobutton">
                                            <input type="radio" id="update_CheckField" name="radios" value="Update">
                                         <!-- <label for="update">Update</label> -->
						                </div>    
                            </td>
                            <td class="radioTextWidth">
                              <span class="agentRadioButton" data-translationKey="agentLoginScreen_Update"></span>
                            </td>
                            
                            <td class="radioButtonWidth">
                                       <div class="agentRadiobutton">
                                            <input type="radio" id="delete_CheckField" name="radios" value="Delete">
                                           <!--  <label for="delete">Delete</label> -->
						                </div>   
                            </td>
                            <td class="radioTextWidth">
                                 <span class="agentRadioButton" data-translationKey="agentLoginScreen_Delete"></span>
                            </td>
                            <td class="radioButtonWidth">
                                       <div class="agentRadiobutton">
                                            <input type="radio" id="search_CheckField" name="radios" value="Search">
                                            <!-- <label for="search">Search</label> -->
						                </div>      
                            </td>
                            <td class="radioTextWidth">
                               <span class="agentRadioButton" data-translationKey="agentLoginScreen_Search"></span>
                            </td>
                            
                        </tr>
             </table>
             </div>
            </center>
            <br>
             <center>
                <span id="agentAttributionScreen_submitButton" class="button greenflat loginButtonWidth">
                    <span class="buttonText" data-translationKey="agentAttribution_Submit_Button"></span>
                </span>
                <br>
            </center>
        </div>
        <br>
        <br>
        <br>
        <br>
        <br>
    </div>
</script>

<script id="WICIChooseProductScreen-template" type="text/x-jquery-tmpl">
    <div class="ScreenLayout" id="chooseProductScreen_PageContents">
    		
		{{if activationItems.getModel('loginScreen').get('printerMacAddress') && activationItems.getModel('loginScreen').get('printerInRange')}}
			<div class="printerConnected" data-translationKey="chooseProduct_PrinterConnected"></div>
		{{else}}
			<div class="printerNotConnected" data-translationKey="chooseProduct_PrinterNotConnected"></div>			
		{{/if}}

		<div class="overlay" id="handoutTabToCustomerDialog-container">
  			<div class="handoutTabToCustomerpopup_chooseProduct">
    			<p class="text-left" data-translationKey="chooseProduct_HandoutTabToCustomerDialogContent"></p>
    			<div>      				
      				<button class="dialog-btn-ok" id="handoutOk" data-translationKey="chooseProduct_HandoutTabToCustomerDialogOk"></button>
					<button class="dialog-btn-cancel" id="handoutCancel" data-translationKey="chooseProduct_HandoutTabToCustomerDialogCancel"></button>
    			</div>
				<center class="retrieveAppPadding">
					<div>      				
      					<button class="dialog-btn-retrieve width200" id="retrieveButton" data-translationKey="chooseProduct_HandoutTabToCustomerDialogRetrieve"></button>
    				</div>
				</center>
  			</div>
		</div>

		<!-- VZE-107 -->
		<div class="overlay" id="dialog_container_customer_signature">
  			<div id="customerSignatureWarningDIV" class="popup_SignatureDialog_Red">
  			    <div id="customerSignatureHeader" class="chooseProduct_SignatureTitle_Red"><center><p class="signatureDialogTitleFont" data-translationKey="chooseProduct_Customer_signatureBoxDialog_title"></center></p></div>
  			    <div class="signatureDialogpadding" class="forntSize10">  
	                  {{if activationItems.getModel('loginScreen').get('employerID').toUpperCase() === "E" && outletProvince !== "QC"}}
                        <p class="text-left" data-translationKey="chooseProduct_Customer_signatureBoxDialog_Content_CSR_ROC"></p>
    					<p class="text-left" data-translationKey="chooseProduct_Customer_signatureBoxDialog_ListTitle_CSR_ROC"></p>
							<ul class="text-left">
								<li data-translationKey="chooseProduct_Customer_signatureBoxDialog_ListItem1_CSR_ROC"></li>
								<li data-translationKey="chooseProduct_Customer_signatureBoxDialog_ListItem2_CSR_ROC"></li>
							</ul>
                       {{else activationItems.getModel('loginScreen').get('employerID').toUpperCase() === "E" && outletProvince == "QC"}}
                              <p class="text-left" data-translationKey="chooseProduct_Customer_signatureBoxDialog_Content_CSR_QC_And_FMR_AllProvince"></p>
    					      <p class="text-left" data-translationKey="chooseProduct_Customer_signatureBoxDialog_ListTitle_CSR_QC_And_FMR_AllProvince"></p>
							    <ul class="text-left">
								   <li data-translationKey="chooseProduct_Customer_signatureBoxDialog_ListItem1_CSR_QC_And_FMR_AllProvince"></li>
								   <li data-translationKey="chooseProduct_Customer_signatureBoxDialog_ListItem2_CSR_QC_And_FMR_AllProvince"></li>
								   <li data-translationKey="chooseProduct_Customer_signatureBoxDialog_ListItem1_CSR_QC_And_FMR_AllProvince_1"></li>
								   <li data-translationKey="chooseProduct_Customer_signatureBoxDialog_ListItem1_CSR_QC_And_FMR_AllProvince_2"></li>
							    </ul>
                       {{else activationItems.getModel('loginScreen').get('employerID').toUpperCase() !== "E" && outletProvince !== "QC"}}
                              <p class="text-left" data-translationKey="chooseProduct_Customer_signatureBoxDialog_Content_CSR_QC_And_FMR_AllProvince"></p>
    					      <p class="text-left" data-translationKey="chooseProduct_Customer_signatureBoxDialog_ListTitle_CSR_QC_And_FMR_AllProvince"></p>
							    <ul class="text-left">
								   <li data-translationKey="chooseProduct_Customer_signatureBoxDialog_ListItem1_CSR_QC_And_FMR_AllProvince"></li>
								   <li data-translationKey="chooseProduct_Customer_signatureBoxDialog_ListItem2_CSR_QC_And_FMR_AllProvince"></li>
							    </ul>
					   {{else activationItems.getModel('loginScreen').get('employerID').toUpperCase() !== "E" && outletProvince == "QC"}}
							  <p class="text-left" data-translationKey="chooseProduct_Customer_signatureBoxDialog_Content_CSR_QC_And_FMR_AllProvince"></p>
    					      <p class="text-left" data-translationKey="chooseProduct_Customer_signatureBoxDialog_ListTitle_CSR_QC_And_FMR_AllProvince"></p>
							    <ul class="text-left">
								   <li data-translationKey="chooseProduct_Customer_signatureBoxDialog_ListItem1_CSR_QC_And_FMR_AllProvince"></li>
								   <li data-translationKey="chooseProduct_Customer_signatureBoxDialog_ListItem2_CSR_QC_And_FMR_AllProvince"></li>
								   <li data-translationKey="chooseProduct_Customer_signatureBoxDialog_ListItem1_CSR_QC_And_FMR_AllProvince_1"></li>
								   <li data-translationKey="chooseProduct_Customer_signatureBoxDialog_ListItem1_CSR_QC_And_FMR_AllProvince_2"></li>
							    </ul>
                       {{/if}}
    			
				<center>
                   <div class="signatureContainer signatureFixCustomer" >
					  <div class="signatureBorder" id="chooseProductScreen_SignatureContainer">
					      <div class="signature" id="chooseProductScreen_signature"/>
					  </div>
				  </div>
                  <br>
				  <span id="chooseProductScreen_signature_Reset_Button" class="buttonWithSquareBorder grayflat sigResetButton proceedButtonWidth">
					   <span class="buttonText" data-translationKey="signatureScreen_Reset_Button_Label"></span>
				  </span>
                </center>
				<br>
    			<div>      				
      				<button class="dialog-btn-proceed" id="signatureDialogIAgree" data-translationKey="chooseProduct_Customer_signatureBoxDialog_IAgree"></button>
					<button class="dialog-btn-cancel" id="signatureDialogCancel" data-translationKey="chooseProduct_DialogCancel"></button>
    			</div>
    			</div>
  			</div>
		</div>	
	<!-- VZE-107 --->
		<br />
		<div id="chooseProductScreen_PageHeader">
            <span class="pageTitle" data-translationKey="chooseProduct_ChooseOneOfTheCreditCards"></span>
            <span class="pageTitle pageTitleCSR" style="display: none;" data-translationKey="chooseProduct_CanadianTireOptionsMC"></span>
            <br />
            <br />
            <ul id="creditCardProductList">
                <li>
                    <a href="#" id="omcCard" class="creditCardProductClickableProduct" data-lang-class>
                        <span class="CC_Labels" data-translationKey="chooseProduct_OptionsMasterCard"></span>
                    </a>
                    
                </li>
				<li>
                    <a href="#" id="ompCard" class="creditCardProductClickableProduct">
                        <span class="CC_Labels" data-translationKey="chooseProduct_GasAdvantageMasterCard"></span>
                    </a>
                </li>
                <!-- US3766 //-->
                <!-- US5391 Remove OMR from card Selection page -->
				<!--<li class="OMRPortrait" >
                    <a href="#" id="omrCard" class="creditCardProductClickableProduct" data-lang-class>
                        <span class="CC_Labels" data-translationKey="chooseProduct_CashAdvantageMasterCard"></span>
                    </a>
                </li> -->
            </ul>
        </div>

        <div id="cardDescriptionArea">
        </div>
        <br />
        <br />
		<center>
		<div id="chooseProduct_PromoCodeArea" >
            <table id="promoCodeTable" class="stretchThisTable">
    			<tr class="fieldRow">
                    <td class="fieldLabelsTopCell fieldCellSize33">
                        <span data-translationKey="chooseProduct_Program"></span>
                    </td>
                    <td class="fieldValuesTopCell">
						<select id="programDropDown" class="fieldValuesTextField" tabindex=1>
						</select>
                    </td>
                </tr>
                <tr class="fieldRow">
                    <td class="fieldLabelsCell fieldCellSize33">
                        <span data-translationKey="chooseProduct_PromoCode"></span>
                    </td>
                <!-- US3767 - Start //-->
                    <td class="fieldValuesCell">                       
                       <select id="promoCodeDropDown" class="fieldValuesTextField" tabindex=1>
					   </select>
                    </td>
                </tr>
                                
                <tr class="fieldRow"> 
                <!-- US4194 - Start //-->
                    {{if activationItems.getModel('loginScreen').get('retailNetWork') == "MARKS"}}			
							<td class="fieldLabelsCell fieldCellSize33">
		                        <span data-translationKey="chooseProduct_PromoCode"></span>
		                    </td>
					{{else activationItems.getModel('loginScreen').get('retailNetWork') == "PC"}}
							<td class="fieldLabelsCell fieldCellSize33">
		                        <span data-translationKey="chooseProduct_PromoCode"></span>
		                    </td>	
					{{else}}
					<!-- US4433 - Start //-->
						{{if activationItems.getModel('loginScreen').get('employerID').toUpperCase() !== "E"}}
									{{if activationItems.getModel('loginScreen').get('retailNetWork') == "SPORTS"}}
                    	               <td class="fieldLabelsCell fieldCellSize33">
		                                    <span data-translationKey="chooseProduct_PromoCode"></span>
		                                </td>
		                              {{else}}			
							            <td class="fieldLabelsCell fieldCellSize33">
		                                   <span data-translationKey="chooseProduct_PromoCode_Other"></span>
		                                </td>
		                            {{/if}}
		            <!-- US4433 - End //-->      
		                {{else}}
							<td class="fieldLabelsCell fieldCellSize33">
	                        	<span data-translationKey="chooseProduct_PromoCode"></span>
	                    	</td>
                    	{{/if}}
					{{/if}}
					<!-- US4194 - End //--> 					
               <!-- US3767 - End //-->                  
                    <td class="fieldValuesCell">
                        <input class="fieldValuesTextField upperCaseField" id="promoCodeTextField" type="text" tabindex=2 maxlength="5"/>
                    </td>
                </tr>
                <tr class="fieldRow">
                    <td class="fieldLabelsBottomCell fieldCellSize33">
                        <span data-translationKey="chooseProduct_Province"></span>
                    </td>
                    <td class="fieldValuesBottomCell">
						<select id="provinceTextField" class="fieldValuesTextField" tabindex=3>
						</select>
                    </td>
                </tr>
            </table>
		</div>
		</center>
		<br />
		
		<!-- ------------------ VZE-108 Starts ------------------ -->
		
	    {{if activationItems.getModel('loginScreen').get('employerID').toUpperCase() === "E" && outletProvince !== "QC"}}	 
	
		 <div id="receive_CostOfCredit_CheckBox_Area" class="contactInfoCheckBoxOuterBox">
				<div class="agreementBoxContainerEX" style="display: table" >
					<div class="rightContaier">
						<div class="contactInfoCheckBox">
							<input type="checkbox" id="receive_CostOfCredit_CheckBox"> <label for="receive_CostOfCredit_CheckBox"></label>
						</div>
					</div>
					<div class="leftContainer opCheckBoxLabel fontSize16 bold" style="padding-top: 0; display: table-cell; vertical-align: middle;">
						<label data-translationkey="chooseProduct_received_and_reviewed"></label>
					</div>
			  </div>
		</div>
		
		<p class="fontSize25 bold paragraphAlignment"><span data-translationKey="chooseProduct_Application_Disclosures"></span></p>
       <br>
       <div id="cantireCard_Agreement_CheckBoxArea" class="contactInfoCheckBoxOuterBox">
				<div class="agreementBoxContainerEX" style="display: table" >
				
					<div class="rightContaier">
						<div class="contactInfoCheckBox">
							<input type="checkbox" id="cantireCard_Agreement_CheckBox"> <label for="cantireCard_Agreement_CheckBox"></label>
						</div>
					</div>
					
					<div class="leftContainer opCheckBoxLabel fontSize16 bold" style="padding-top: 0; display: table-cell; vertical-align: middle;">
							<label data-translationkey="chooseProduct_CanadianTireCardAgreementCheckBoxText"></label>
					</div>
					
			  </div>
		</div>
		<br>
		
		<p class="overviewCenterTableTitle bold paragraphAlignment"><span data-translationKey="chooseProduct_Triangle_reward_Program"></span></p>
		
		<table class="stretchThisTable borderZero" >
	 	  	 <div class="rightContaier align-vertical-middle-item">
					<div class="cantire_Agreement">
							<p class="receiveEmail_TextArea" data-translationKey = "chooseProduct_TriangleRewardsProgram_para1"></p>
							<p class="receiveEmail_TextArea" data-translationKey = "chooseProduct_TriangleRewardsProgram_para2"></p>
							<p class="receiveEmail_TextArea" data-translationKey = "chooseProduct_TriangleRewardsProgram_para3"></p>
							<p class="receiveEmail_TextArea" data-translationKey = "chooseProduct_TriangleRewardsProgram_para4"></p>
							<p class="receiveEmail_TextArea" data-translationKey = "chooseProduct_TriangleRewardsProgram_para5"></p>
							<p class="receiveEmail_TextArea" data-translationKey = "chooseProduct_TriangleRewardsProgram_para6"></p>
							<p class="receiveEmail_TextArea" data-translationKey = "chooseProduct_TriangleRewardsProgram_para7"></p>
							<p class="receiveEmail_TextArea" data-translationKey = "chooseProduct_TriangleRewardsProgram_para8"></p>
							<p class="receiveEmail_TextArea" data-translationKey = "chooseProduct_TriangleRewardsProgram_para9"></p>
							<p class="receiveEmail_TextArea" data-translationKey = "chooseProduct_TriangleRewardsProgram_para10"></p>
							<p class="receiveEmail_TextArea" data-translationKey = "chooseProduct_TriangleRewardsProgram_para11"></p>
							<p class="receiveEmail_TextArea" data-translationKey = "chooseProduct_TriangleRewardsProgram_para12"></p>
							<p class="receiveEmail_TextArea" data-translationKey = "chooseProduct_TriangleRewardsProgram_para13"></p>
							<p class="receiveEmail_TextArea" data-translationKey = "chooseProduct_TriangleRewardsProgram_para14"></p>
							<p class="receiveEmail_TextArea" data-translationKey = "chooseProduct_TriangleRewardsProgram_para15"></p>
							<p class="receiveEmail_TextArea" data-translationKey = "chooseProduct_TriangleRewardsProgram_para16"></p>
					</div>
		     </div>
	   </table>
       <br>
       <div id="triangle_Rewards_Program_TermsAndCondition_CheckBox_Area" class="contactInfoCheckBoxOuterBox">
			<div class="agreementBoxContainerEX" style="display: table" >
			
				<div class="rightContaier">
					<div class="contactInfoCheckBox">
						<input type="checkbox" id="triangle_Rewards_Program_TermsAndCondition_CheckBox"> <label for="triangle_Rewards_Program_TermsAndCondition_CheckBox"></label>
					</div>
				</div>
				
				<div class="leftContainer opCheckBoxLabel fontSize16 bold" style="padding-top: 0; display: table-cell; vertical-align: middle;">
						<label data-translationkey="chooseProduct_Triangle_RewardsProgramTnC"></label>
				</div>
				
			</div>
		</div>
		
		{{/if}}
		
	<!-- ------------------ VZE-108 Ends ------------------ -->
		
		<br />
		<center>
            <span id="chooseProductScreen_ApplyNowButton" class="button grayflat applyButtonWidth">
            <!-- US4571 start -->
            <span class="buttonText" data-translationKey="chooseProduct_ApplyNow_Button_Label"></span>
            <!-- US4571 ends -->
            <!-- US4495 - Start //-->
              <!--  {{if activationItems.getModel('loginScreen').get('employerID').toUpperCase() !== "E"}}
            	     <span class="buttonText" data-translationKey="chooseProduct_ApplyNow_Button_Label_with_testprint"></span>
            	{{else}}
            	     <span class="buttonText" data-translationKey="chooseProduct_ApplyNow_Button_Label"></span>
            	{{/if}}  -->
            <!-- US4495 - End //-->   	
			</span>
        </center>
		<br />
		<br />
        <hr>
    </div>
	<div id="chooseProductScreen_disclaimerArea" class="ScreenLayoutFullWidth"></div>
</script>

<script id="WICIContactInfoScreen-template" type="text/x-jquery-tmpl">
    <div class="ScreenLayout" id="EmailInfoSCreen_PageContents">

<div class="overlay" id="email_dialog_container_customer_signature">
    <div id="customerSignatureWarningDIV" class="popup_SignatureDialog_Red">
        <div id="customerSignatureHeader">
		<h1 id= "contactInfoClose" class="contactInfoClose">X</h1>
            <center>
                <p id="email_yes_title" style="color:red;font-size: 28px;" data-translationKey="ContactInfo_yes_signatureBoxDialog_title">
                <p id="email_no_title" style="color:red;font-size: 28px;" data-translationKey="ContactInfo_no_signatureBoxDialog_title">
            </center>
            </p>
        </div>
        <div class="signatureEmailDialogpadding" class="forntSize10">
            <div id="email_yes">
                <p class="fontSize20" data-translationKey="ContactInfo_yes_email"></p>
				<p id="emailRetrive" class="fontSize24" ></p>
                
                <p class="fontSize20" data-translationKey="ContactInfo_yes_para1"></p>
                
                <p class="fontSize20" data-translationKey="ContactInfo_yes_para2"></p>
            </div>
            <div id="email_no">
                <p class="fontSize20" data-translationKey="ContactInfo_no_para1"></p>
                <br>
            </div>
            
            <center>
                <div class="signatureContainer signatureFixCustomer">
                    <div class="signatureBorder" id="contactInfoScreen_SignatureContainer">
                        <div class="signature" id="contactInfoScreen_signature" />
                    </div>
                </div>
                <br>
                <span id="signature_Reset_Button"
                    class="buttonWithSquareBorder grayflat sigResetButton proceedButtonWidth">
                    <span class="buttonText" data-translationKey="contactInfoScreen_Reset_Button_Label"></span>
                </span>
            </center>
            <br>
            <div>
                <button class="dialog-btn-proceed" id="DialogAgree"
                    data-translationKey="ContactInfo_dialog_confirm"></button>
                <button class="dialog-btn-cancel" id="DialogCancel"
                    data-translationKey="ContactInfo_dialog_cancel"></button>
            </div>
        </div>
    </div>
</div>

		<div>
			<p class="bold fontSize40 marginBlockEnd0" data-translationKey="contactInfo_Heading1"></p>
			<p class="fontSize25 marginBlockStart25"><span data-translationKey="contactInfo_Para1"></span></p>
			<p id="contactinfo_HeadingId" class="bold fontSize30 marginStartEnd"><span id="i_icon" data-translationKey="contactInfo_Heading2"></span>
			 <span>
				<a class="tooltip contactInfomation_i_icon" id="contactInfomation_infomation_phone">
             		<span class="tooltipphonetext" data-translationKey="contactInfo_PhoneToolTipMsg"></span>
                </a>
             </span><br>  
			</p>
			<p class="fontSize20 colorGray"><span data-translationKey="contactInfo_Para2"></span></p>
			<center>
		    	<table id="personalInfoTable" class="stretchThisTable personalInfoPhoneTable">
		        	<tr class="fieldRow">
		            	<td class="fieldLabelsTopCell fieldCellSize33">
		                	<span data-translationKey="personalData_PrimaryPhone"></span>
		                </td>
		                <td class="fieldValuesCell">
		                	<input class="contactInfoFieldValues contactInfoUIMask" id="contactInfo_HomePhone_TextField" type='tel' maxlength="10" tabindex=28/>
		                </td>
		            </tr>
	        	</table>
            </center>
            <br>
	        <table class="radioButtonTableNoBorderPersonalInfo elementWithLeft25"  id="primaryPhoneRadioGroup">
            	<tr>
                	<td>
                    	<div class="agentRadiobutton">
                        	<input type="radio" id="primaryLandline_CheckField" name="primayRadios" value="primaryLandline">
                        </div> 
                    </td>
                    <td class="radioTextWidth">
                    	<span class="agentRadioButton" data-translationKey="personalData_Landline"></span>
                    </td>
                    <td>
                    	<div class="agentRadiobutton">
                        	<input type="radio" id="primaryMobile_CheckField" name="primayRadios" value="primaryMobile">
						</div>    
                    </td>
                    <td class="radioTextWidth">
                    	<span class="agentRadioButton" data-translationKey="personalData_Mobile"></span>
                    </td>
               </tr>
            </table>
            <br>
            <br>
            <center>
	        	<table id="personalInfoTable" class="stretchThisTable personalInfoPhoneTable">
		        	<tr class="fieldRow">
		            	<td class="fieldLabelsTopCell fieldCellSize33">
		                	<span data-translationKey="personalData_SecondaryPhone"></span>
		                </td>
		                <td class="fieldValuesBottomCell">
		                	<input class="contactInfoFieldValues contactInfoUIMask" id="contactInfo_CellPhone_TextField" type='tel' maxlength="10" tabindex=29/>
		                </td>
		            </tr>
	        	</table>
            </center>
            <br>
           <table class="radioButtonTableNoBorderPersonalInfo elementWithLeft25" id="secondaryPhoneRadioGroup">
           		<tr>
                	<td>
                    	<div class="agentRadiobutton">
                        	<input type="radio" id="secondaryLandline_CheckField" name="secondaryRadios" value="secondaryLandline">
                        </div> 
                    </td>
                    <td class="radioTextWidth">
                    	<span class="agentRadioButton" data-translationKey="personalData_Landline"></span>
                    </td>
                    <td>
                    	<div class="agentRadiobutton">
                        	<input type="radio" id="secondaryMobile_CheckField" name="secondaryRadios" value="secondaryMobile">
						</div>    
                    </td>
                    <td class="radioTextWidth">
                    	<span class="agentRadioButton" data-translationKey="personalData_Mobile"></span>
                    </td>
                </tr>
            </table>
			<br>
			<br>

			<p class="fontSize25 bold paragraphAlignmentStartEndPoint5"><span data-translationKey="contactInfo_Heading3"></span></p>
			<p class="fontSize20 colorGray paragraphAlignmentStartPoint5"><span data-translationKey="contactInfo_Para3_1"></span></p>
			<p class="fontSize20 colorGray paragraphAlignmentStartPoint5"><span data-translationKey="contactInfo_Para3_2"></span></p>
			<p class="fontSize24 bold paragraphAlignmentStartEndPoint5"><span data-translationKey="contactInfo_EmailConsent"></span></p>
			<table class="radioButtonTableNoBorderPersonalInfo elementWithLeft25" id="emailRadioGroup">
           		<tr>
                	<td>
                    	<div class="agentRadiobutton">
                        	<input type="radio" id="emailYes_CheckField" name="emailRadios" value="secondaryLandline">
                        </div> 
                    </td>
                    <td class="emailradioTextWidth">
                    	<span class="agentRadioButton" data-translationKey="contactInfo_EmailConsent_yes"></span>
                    </td>
				</tr>
				<tr>
                    <td>
                    	<div class="agentRadiobutton">
                        	<input type="radio" id="emailNo_CheckField" name="emailRadios" value="secondaryMobile">
						</div>    
                    </td>
                    <td class="emailradioTextWidth">
						<div style="margin-left:8px;" >
                    	<span class="emailTextButton"  data-translationKey="contactInfo_EmailConsent_no"></span>
						</div>
                    </td>
                </tr>
            </table>
				<br>
				<br>

			<center>
	        	<table id="personalInfoTable" class="stretchThisTable personalInfoPhoneTable">
		        	<tr class="fieldRow" id="email_table">
		            	<td class="fieldLabelsTopCell fieldCellSize33">
		                	<span data-translationKey="contactInfo_EmailAddress"></span>
		                </td>
		                <td class="fieldValuesBottomCell" style="border-radius: 0 11px 0 0;">
		                	<input class="contactInfoFieldValues" id="contactInfo_EmailAddress_TextField" type='email' maxlength="60"/>
		                </td>
		            </tr>
	        	</table>
            </center>

			<p class="fontSize30 bold paragraphAlignmentHeading4"><span data-translationKey="contactInfo_Heading4"></span></p>
			<p class="fontSize20 colorGray paragraphAlignmentStartPoint5"><span data-translationKey="contactInfo_Para41"></span></p>

			<div class="contactInfoCheckBoxOuterBox">
				<div class="agreementBoxContainerEX" style="display: table" >
					<div class="rightContaier">
						<div class="contactInfoCheckBox">
							<input type="checkbox" id="receiveEstmtConsent_CheckBox"> <label for="receiveEstmtConsent_CheckBox"></label>
						</div>
					</div>
					<div class="leftContainer opCheckBoxLabel fontSize20" style="padding-top: 0; display: table-cell; vertical-align: middle;">
						<label data-translationkey="contactInfo_Para3"></label>
						<label  id="QC_I_Confirm1" data-translationkey="contactInfo_Para3_QC1"></label>
						<label  id="QC_I_Confirm2" data-translationkey="contactInfo_Para3_QC2"></label>
					</div>
				</div>
			</div>
            <br><br>
			<!-- <p class="fontSize25 bold paragraphAlignment"><span data-translationKey="contactInfo_Heading5"></span></p> -->
            <!-- WICI-170 --->
				<center>
			               <div>
	                           <table id="titleTable_Language" class="stretchThisTable" >
	                             <tr >
	                                 <td id="title_eStatment_td"class="fieldLabelsSingleCellYesNo">
	                                      <p id="eStatmenent_title" class="fontSize25 bold paragraphAlignment"><span></span></p>
	                                  </td>
	                               	  <td id="language_Slider_for_QC" class="fieldValuesOneCellFlipYesNo fieldValuesCellFlipYesNo">
	                               	  <center>
 										<div class="btn-container">
											<label class="switch btn-color-mode-switch labelForToggle">
												<input id = "language_slider"  type="checkbox" name="color_mode"  value="1">
												<label for="color_mode" data-off="FRANÇAIS" data-on="ENGLISH"  class="labelForToggle labelFont btn-color-mode-switch-inner"></label>
											</label>
										</div>
										</center>
	                                 </td>                                                      
	                            </tr>
	                          </table>
			 </center>
            <!-- WICI-170 --->
			<table class="stretchThisTable borderZero" >
 	  			<div class="rightContaier align-vertical-middle-item">
						<div  id="bill96_text_QC" class="receiveEstmt_CheckBox">
							<p  id="p1_QC" class="receiveEmail_TextArea bold" ></p>
							<p  id="p2_QC" class="receiveEmail_TextArea" >
					    </div>
	    		</div>
			</table>
		
			<p class="fontSize30 bold paragraphAlignmentHeading4"><span data-translationKey="contactInfo_Heading6"></span></p>
			<p class="fontSize25 paragraphAlignmentStartZero" data-translationKey="contactInfo_Para5"></p>

		</div>
	</div>
	<div class="padding5centBlock">
		<div>
			{{if activationItems.getModel('chooseProductModel').get('productCard') == "OMX"}}
				<div class="contactInfo5CentMoney">
					<div style="display: table">
						<div class="rightContaier contactInfoLeftTriangleImage">
							<div>
								<img src="app/images/contactInfo5centIcon.png" width="80">
							</div>
						</div>
						<div class="leftContainer opCheckBoxLabel fontSize20" style="padding-top: 0; display: table-cell; vertical-align: middle;">
							<label data-translationkey="contactInfo_Heading7"></label>
						</div>
					</div>
				</div>
		    {{/if}} 
		</div>
	</div>
	<br>
	<div class="contactInfoBottomLayout ScreenLayout">
		<div>
			<div class="contactInfoCheckBoxOuterBox">
				<div class="agreementBoxContainerEX" style="display: table">
				<div class="rightContaier contactInfoCheckboxAlignment">
				<div class="contactInfoCheckBox">
					<input type="checkbox" id="receiveEmail_CheckBox"> <label for="receiveEmail_CheckBox"></label>
				</div>
				</div>
				<div class="leftContainer opCheckBoxLabel fontSize25" style="padding-top: 0; display: table-cell; vertical-align: middle;">
					<label data-translationkey="contactInfo_Para6"></label>
				</div>
			</div>
		</div>
	</div>
	<br>
	<br>

</script>

<script id="WICIFinancialScreen-template" type="text/x-jquery-tmpl">
	<div class="ScreenLayout" id="FEScreen_PageContents">
		<br/>
		<br/>		
		<div>
			<span id="FEClickHere" class="pageTitle" data-translationKey="finEmpInfo_PageTitle"></span>
		</div>
		<br/>
		<br/>
		<div>
			<span class="financial_info_Labels" data-translationKey="finEmpInfo_PageSubTitle"></span>
		</div>
		<br/>
		<br/>
		<center>
			<table class="stretchThisTable" id="finEmpInfo_EmpType_Group">
				<tr>
					<td class="fieldLabelsSingleCell ">
						<span class="fieldLabelsTitle" data-translationKey="finEmpInfo_EmpType"></span>						
					</td>
					<td class="fieldValuesOneCell">
						 <select class="fieldValuesTextField" id="finEmpInfo_EmplType_TextField" tabindex=1>
					</td>
				</tr>
			</table>
		</center>	
		<br/>
		<br/>
		<center>
			<div id="FE_EmploymentArea" >
	            <table id="FE_EmploymentArea_Table" class="stretchThisTable">
	                <tbody id="FE_EmploymentArea1" >
		                <tr class="fieldRow">
		                    <td class="fieldLabelsTopCell fieldCellSize33">
		                        <span data-translationKey="finEmpInfo_JobCategory"></span>
		                    </td>
		                    <td class="fieldValuesTopCell">
		                        <select class="fieldValuesTextField" id="finEmpInfo_JobCategory_SelectField" tabindex=2>
		                    </td>
		                </tr>
		                <tr class="fieldRow">
		                    <td class="fieldLabelsCell fieldCellSize33">
		                        <span data-translationKey="finEmpInfo_JobDescription"></span>
		                    </td>
		                    <td class="fieldValuesCell fincJobDescFont">
		                    	<div class="autocomplete">
    								<input autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false" class="fieldValuesTextField" id="finEmpInfo_JobDescription_Input_TextField" type="text" tabindex=3 /> 
  							    </div>				
		                    </td>
		                </tr>		                
		                <tr class="fieldRow" id="finEmpInfo_JobDescriptionOtherArea">
		                    <td class="fieldLabelsCell fieldCellSize33">
		                        <span data-translationKey="finEmpInfo_JobTitleOther"></span>
		                    </td>
		                    <td class="fieldValuesCell">		                        
		                        <input autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false" class="fieldValuesTextField upperCaseField" id="finEmpInfo_JobDescription_TextField" type="text" maxlength="30" tabindex=4/>
		                    </td>
		                </tr>
		                <tr class="fieldRow">
		                    <td class="fieldLabelsCell fieldCellSize33">
		                        <span data-translationKey="finEmpInfo_EmployerName"></span>
		                    </td>
		                    <td class="fieldValuesCell">
		                        <input class="fieldValuesTextField upperCaseField" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false" id="finEmpInfo_EmployerName_TextField" type="text" maxlength="36" tabindex=4/>
		                    </td>
		                </tr>
		                <tr class="fieldRow">
		                    <td class="fieldLabelsCell fieldCellSize33">
		                        <span data-translationKey="finEmpInfo_EmployerCity"></span>
		                    </td>
		                    <td class="fieldValuesCell">
		                        <input class="fieldValuesTextField upperCaseField" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false" id="finEmpInfo_EmployerCity_TextField" type="text" maxlength="24" tabindex=5/>
		                    </td>
		                </tr>
		                <tr class="fieldRow">
		                    <td class="fieldLabelsCell fieldCellSize33">
		                      <p class="marginStartEnd">
		                        <span class="leftAlign" data-translationKey="finEmpInfo_EmployerPhone"></span>
		                        <span>
				                   <a class="tooltip financialInfomation_i_icon" id="financialScreen_infomation_phone">
             		               		<span class="tooltipphonetextFinSuppPage" data-translationKey="contactInfo_PhoneToolTipMsg"></span>
                                   </a>
                                </span>
                                </p>
		                    </td>
		                    <td class="fieldValuesCell">
		                        <input class="fieldValuesPhoneField" id="finEmpInfo_EmployerPhone_TextField" type='tel' maxlength="10" tabindex=6/>
		                    </td>
		                </tr>
		                <tr class="fieldRow">
		                    <td class="fieldLabelsBottomCell fieldCellSize33">
		                        <span data-translationKey="finEmpInfo_HowLongCurrentEmployer"></span>
		                    </td>
							<td>
		                    	<div id="finEmpInfo_Duration">
			                    	<table class="durationControlTable">
									   	<tr>
									   		<td class="fieldValuesCell>
											<div>
												<div id="durationControlDiv">
													<table class="durationTableNoBorder">
														<tr>
															<td id="durationSpanSeparator" class="fieldValuesCell fieldCellSize5 durationSpanMinWidth">
									   							<span data-translationKey="personalData_Address_DurationYears"></span>&emsp;
									   						</td>
								   			
								   							<td id="durationCellsSeparator" class="fieldValuesCell fieldCellSize50">
									   							<div id="durationControlLeftPadding" class="numbers-row">
		        													<input type="tel" class="durationInput" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false" maxlength="3" id="finEmpInfo_Years_Slider" min="0" max="100" tabindex=7>
		      													</div>
	      													</td>
														</tr>	
													</table>
												</div>
	
												<div id="durationControlDiv">
													<table class="durationTableNoBorder">
														<tr>
															<td class="fieldValuesCell fieldCellSize5 durationSpanMinWidth">
									   							<span data-translationKey="personalData_Address_DurationMonths"></span>&emsp;
									   						</td>
	
									   						<td id="durationCellsSeparatorPadding" class="fieldValuesCell fieldCellSize50">
										   						<div id="durationControlLeftPadding" class="numbers-row" >
		    	    												<input type="tel" class="durationInput" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false" maxlength="2" id="finEmpInfo_Months_Slider" min="0" max="11" tabindex=8>
		      													</div>
	      													</td>
														</tr>	
													</table>
												</div>		
										
											</div>
											</td>
								   		</tr>								   	
									</table>
								</div>
							</td>
		                </tr>
		            </tbody>
	            </table>
			</div>
		</center>
		<br/>
		<br/>
		
		<div>
			<span class="pageTitle" data-translationKey="finEmpInfo_IncomeTitle"></span>
		</div>
		<br/>
		<br/>
		<div>
			<span class="financial_info_Labels" data-translationKey="finEmpInfo_IncomeSubTitle"></span>
		</div>

		<br/>
		<br/>
		<br/>
		
		<center>
	            <table class="stretchThisTable">
					<tbody>
		                <tr class="fieldRow">
		                    <td id="finEmpInfo_GrossIncome_Label" class="fieldLabelsTopIncomeCell fieldCellSize33">
		                        <span data-translationKey="finEmpInfo_GrossIncome"></span>
		                    </td>
		                    <td class="fieldValuesCell">
								<div class="autocomplete">
		                        	<input id="finEmpInfo_GrossIncome_TextField" class="fieldValuesTextField" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false" maxlength="6" type='tel' tabindex=11/>
			                	</div>    
							</td>
		                    <!--<td class="fieldValuesTopCell">
		                        <select class="fieldValuesTextField" id="finEmpInfo_PersonalIncomeFrequency_SelectField" tabindex=2>
		                    </td>-->
		                </tr>
		                <tr class="fieldRow">
		                    <td id="finEmpInfo_GrossHouseholdIncome_Label" class="fieldLabelsBottomIncomeCell fieldCellSize33">
		                        <span data-translationKey="finEmpInfo_GrossHouseholdIncome"></span>
		                    </td>
		                    <td id="finEmpInfo_GrossHouseholdIncome_Value" class="fieldValuesBottomCell">
		                        <input class="fieldValuesTextField" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false" id="finEmpInfo_GrossHouseholdIncome_TextField" maxlength="6" type='tel' tabindex=11/>
		                    </td>
		                    <!--<td class="fieldValuesCell">
		                        <select class="fieldValuesTextField" id="finEmpInfo_HouseholdIncomeFrequency_SelectField" tabindex=2>
		                    </td>-->
		                </tr>
	                </tbody>
				</table>
		</center>
		
		<br/>
		<br/>
		<br/>

		<center>
			<div id="PersonalIncomeYearlyDisplayArea" class="displayIncomeArea hideElement" >
				<span class="displayIncomeText" data-translationKey="financialData_grossIncomeCalculated"></span><span class="displayIncomeValue" id="grossIncomeYearly"></span><span class="displayIncomeText" data-translationKey="financialData_IncomeCalculatedAnnual"></span>
			</div>
			<div id="BothIncomeYearlyDisplayArea" class="displayBothIncomeArea hideElement" >
				<span class="displayIncomeText" data-translationKey="financialData_grossIncomeCalculated"></span><span class="displayIncomeValue" id="grossPersonalIncomeYearly"></span><span id="householdIncomeText" class="displayIncomeText" data-translationKey="financialData_bothIncomeCalculatedAnnual"></span><span class="displayIncomeValue" id="grossHouseholdIncomeYearly"></span><span class="displayIncomeText" data-translationKey="financialData_IncomeCalculatedAnnual"></span>
			</div>
		</center>

		<br/>
		<br/>
		<br/>
		
		<center>
			<div id="FE_SINArea" >
	            <table id="FE_SINArea_Table" class="stretchThisTable">
					<tbody>
		                <tr class="fieldRow">
		                    <td class="fieldLabelsSingleCell fieldCellSize33">
		                        <span data-translationKey="finEmpInfo_SIN"></span>
		                    </td>
		                    <td class="fieldValuesOneCell">
		                        <input class="fieldValuesTextField" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false" maxlength="9" id="finEmpInfo_SIN_TextField" type='tel' tabindex=12/>
		                    </td>
		                </tr>
	                </tbody>
				</table>
			</div>
		</center>
		<br/>
		<br/>
		<br/>
		<br/>
	</div>	
</script>		
<script id="FinishTrainingScreen-template" type="text/x-jquery-tmpl">
    <div class="ScreenLayout" id="finishTrainingScreen_PageContents">
    		
		
    <br><br><br><br><br><br><br>
     <center>
          <h1>Finish Training</h1>
         <span id="finishTrainingScreen_next" class="button greenflat loginButtonWidth">
               <span class="buttonText" data-translationKey="finishTrainning_Button_label"></span>
         </span>
         <br>
    </center>  			
	</div>
		
</script>
<script id="WICIInstantIssuanceSetupCompleteScreen-template" type="text/x-jquery-tmpl">
    <div class="ScreenLayout" id="pendingScreen_PageContents">
	
        <div data-role="fieldcontain" class="">
				<br><br><br>
					<span class="printPageTitle" data-translationKey="printScreen_Title"></span>
	         	<br><br><br>
				{{if activationItems.getModel('printScreen').get('respCardType') === "OMX" || activationItems.getModel('printScreen').get('respCardType') === "OMZ"}}					
	         		<span class="printPageSubTitle" data-translationKey="${activationItems.getModel('printScreen').get('keyForDisplayingApplicationStatus')}" />
					<span class="printPageSubTitle" data-translationKey="${activationItems.getModel('printScreen').get('keyForDisplayingCardSelection')}"></span>
				{{else}}					
					<span class="printPageSubTitle" data-translationKey="printScreen_SubTitle_OMP_OMR"></span>	
					<span class="printPageSubTitle" data-translationKey="${activationItems.getModel('printScreen').get('keyForDisplayingCardSelection')}"></span>
					<span class="printPageSubTitle" data-translationKey="printScreen_PayParaSymbol"></span>
				{{/if}}				
				<br><br><br>
					<span class="printPageSubTitle" data-translationKey="printScreen_PayInCompletePara1"></span>
				<br><br><br>
					<span class="printPageSubTitle" data-translationKey="printScreen_PayInCompletePara2"></span>
				<br><br><br>
					<span class="printPageSubTitle greenText" data-translationKey="printScreen_PayInCompletePara3"></span>
					<span class="printPageSubTitle greenText" data-translationKey="${activationItems.getModel('printScreen').get('keyForDisplayingCardSelection')}"></span>
					<span class="printPageSubTitle greenText" >!</span>
				<br><br><br>
	    </div>
	
		<br><br><br><br><br>
		
		<center>
            <span id="instantIssuanceComplete_StartNewApplicationButton" class="button greenflat afterPrintButtonWidth uppercase">
            	<span class="buttonText" data-translationKey="printResponseStatus_StartNewApplication_Button"></span>
			</span><br>
			<span id="instantIssuanceComplete_LogOutButton" class="button darkgrayflat afterPrintButtonWidth uppercase">
            	<span class="buttonText" data-translationKey="printResponseStatus_LogOut_Button"></span>
			</span>
        </center>
	
	
	</div>
</script>

<script id="WICIInstantIssuanceSetupInsScreen-template" type="text/x-jquery-tmpl">
    <div class="ScreenLayout" id="pendingScreen_PageContents">
	
	<div data-role="fieldcontain" class="">
			<br>
			<br>
			<br>
			<div>
             <!-- US4787 starts --->
			 <center>
			   <span class="printPageInsScreenTitleForUser" data-translationKey="printScreen_safty_instuction_for_user"></span>
			 </center>
             <!-- US4787 ends --->
			</div>	
			<table class="durationControlTable">
					<tr>
						<td align="center" class="androidApplePaytd androidPaytdWidth">
                             <!-- US4800 starts --->
							{{if activationItems.getModel('mobilePaymentsScreen').get('consentGranted') === 'G' }}
                                 {{if resCardType == "OMX"}}
                                    <a href="#" id="omxCard_setUpPageAndroid"  data-lang-class>
                                 {{/if}}
                                 {{if resCardType === "OMP"}}
                                    <a href="#" id="ompCard_setUpPageAndroid"  data-lang-class>
                                 {{/if}}
                                 {{if resCardType === "OMR"}}
                                    <a href="#" id="omrCard_setUpPageAndroid"  data-lang-class>
                                 {{/if}}
                                 {{if resCardType === "OMZ"}}
                                    <a href="#" id="omzCard_setUpPageAndroid"  data-lang-class>
                                 {{/if}}
								 <!-- <img id="androidAppleMobileImage" src="app/images/and_en.png" data-lang-src width="125%" align="right" style="margin: 10px 0 10px 0;"/> -->
							{{else activationItems.getModel('mobilePaymentsScreen').get('consentGranted') === 'A' }}
								<!-- <img id="androidAppleMobileImage" src="app/images/ios_en.png" data-lang-src width="75%" align="right" style="margin: 10px 30px 10px 0;"/> -->
                                 {{if resCardType == "OMX"}}
                                    <a href="#" id="omxCard_setUpPageIos" data-lang-class>
                                 {{/if}}
                                 {{if resCardType === "OMP"}}
                                    <a href="#" id="ompCard_setUpPageIos" data-lang-class>
                                 {{/if}}
                                 {{if resCardType === "OMR"}}
                                    <a href="#" id="omrCard_setUpPageIos" data-lang-class>
                                 {{/if}}
                                 {{if resCardType === "OMZ"}}
                                    <a href="#" id="omzCard_setUpPageIos" data-lang-class>
                                 {{/if}} 
							{{/if}}
                            <!-- US4800 ends --->
						</td>
						<td class="androidApplePaytd applePaytdWidth">
							<p class="printPageInsScreenTitle marginForInsScreenTitle" data-translationKey="printScreen_SetUpIns"></p>
							<ol class="marginOL">
								<li class="printInsPageSubTitle setupInsListSeparatorPadding" data-translationKey="printScreen_SetUpInsPara1"></li>
								{{if activationItems.getModel('mobilePaymentsScreen').get('consentGranted') === 'G' }}
									<li class="printInsPageSubTitle setupInsListSeparatorPadding" data-translationKey="printScreen_SetUpInsPara2"></li>
								{{else activationItems.getModel('mobilePaymentsScreen').get('consentGranted') === 'A' }}
									<li class="printInsPageSubTitle setupInsListSeparatorPadding" data-translationKey="printScreen_SetUpInsApplePara2"></li>
								{{/if}}
								<li class="printInsPageSubTitle setupInsListSeparatorPadding inheritPadding" data-translationKey="printScreen_SetUpInsPara3"></li>
							</ol>
							<span class="printInsPageSubTitle elementWithLeft50" data-translationKey="printScreen_SetUpInsCardNo"></span>
							<span class="printInsPageSubTitle" data-translationKey=${cardNumber}></span>
							<br>
							<span class="printInsPageSubTitle elementWithLeft50" data-translationKey="printScreen_SetUpInsExpiryDate"></span>
							<span class="printInsPageSubTitle" data-translationKey=${expiryDate}></span>
							<br>
							<table class="setupInsSecurityCodeTable elementWithLeft50">
								<tr>
									<td id="setupInsSecurityCodeLabel" class="setupInsSecurityCodetd setupInsTableWidth">
		                        		<span class="printInsPageSubTitle" data-translationKey="printScreen_SetUpInsSecurityCode"></span>
									</td>
									<td class="setupInsSecurityCodetd">
		                        		<span class="printInsPageSubTitle" data-translationKey="printScreen_SetUpInsSecurityCodeIns"></span>
									</td>
								</tr>
							</table>
							<!--							
							<span id="printScreen_DoneButton" class="button greenflat afterPrintButtonWidth uppercase">
	           					<span class="buttonText" data-translationKey="print_Done_Button"></span>
							</span>
							-->
						</td>
					</tr>
			</table>
        </div>
        <br>
        
       <!-- <br><br><br><br><br> -->
		
	    <center>
            <span id="instantIssuanceSetupIns_StartNewApplicationButton" class="button greenflat afterPrintButtonWidth uppercase">
            	<span class="buttonText" data-translationKey="printResponseStatus_StartNewApplication_Button"></span>
			</span><br>
			<span id="instantIssuanceSetupIns_LogOutButton" class="button darkgrayflat afterPrintButtonWidth uppercase">
            	<span class="buttonText" data-translationKey="printResponseStatus_LogOut_Button"></span>
			</span>
        </center>        
	
	
	</div>
</script>

<script id="WICILoginScreen-template" type="text/x-jquery-tmpl">
    <div class="ScreenLayout" id="loginScreen_PageContents">
        <br>
        <div class="flipAlignRight">
            <select id="language_choser" data-role="slider">
                <option value="E" selected>En</option>
                <option value="F">Fr</option>
            </select>
            <br>
            <br>
                <!-- <span id="loginScreen_TestPrintButton" class="button red login_TestPrintButton rightalign">
                    <span class="buttonText" data-translationKey="settings_testPrintButton"></span>
                </span> -->
        </div>

        <br>
        <br>
        <center>
            <div id="CTFSLogo" class="Login_CanadianTireLogo"></div>
        </center>
        <br>
        <br>
        <br>
        <div id="loginScreen_FieldsArea">
            <center>
                <div class="wrapLoginTable">
                    <table id="loginFieldsTable" class="loginTableWidth">
                        <tr class="fieldRow">
                            <td class="fieldLabelsTopCell fieldSize55">
                                <!--<span data-translationKey="loginScreen_UserID_Label"></span>//-->
                                <span data-translationKey="loginScreen_EmployerID_Label"></span>
                            </td>
                            <td class="fieldValuesTopCell">
                                <!--<input class="fieldValuesTextField upperCaseField" id="userIDTextField" type="text" maxlength="20" tabindex=1/>//-->
                                <input class="fieldValuesTextField upperCaseField" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false" id="employerIDTextField"  type="text" onkeyup="this.value = this.value.toUpperCase();" maxlength="1" tabindex=1/>
                            </td>
                        </tr>
                        <tr class="fieldRow" id="retail_DropDown_Row">
                            <td class="fieldLabelsCell fieldSize55">
                                <span data-translationKey="loginScreen_RetailNetWork_Label"></span>
                            </td>
                            <td class="fieldValuesCell">
                                <select id="retailNetWorkId" class="fieldValuesTextField" tabindex=2></select>
					   
                            </td>
                        </tr>
                        <tr class="fieldRow">
                            <td class="fieldLabelsCell fieldSize55">
                                <span data-translationKey="loginScreen_Location_Number"></span>
                            </td>
                            <td class="fieldValuesCell">
                                <input class="fieldValuesTextField upperCaseField" id="locationNumberTextField" type="text" maxlength="5" tabindex=3/>
                            </td>
                        </tr>
                        <tr class="fieldRow hidden">
                            <td class="fieldLabelsBottomCell fieldSize55">
                                <span data-translationKey="loginScreen_AgentID_Label"></span>
                            </td>
                            <td class="fieldValuesBottomCell">
                                <input class="fieldValuesTextField upperCaseField" id="agentIDTextField" type="text" maxlength="8" tabindex=3/>
                            </td>
                        </tr>
                       
                        <!-- US4454 ---->
                        <tr class="fieldRow hidden">
                            <td class="fieldLabelsBottomCell fieldSize55">
                                <span data-translationKey="loginScreen_Password_Label"></span>
                            </td>
                            <td class="fieldValuesBottomCell">
                                <input class="fieldValuesTextField upperCaseField" id="passwordTextField" type="password" maxlength="6" tabindex=4/>
                            </td>
                        </tr>
                        <!-- US4454 ---->
                        <tr class="fieldRow ">
                            <td class="fieldLabelsCell fieldSize55">
                                <span data-translationKey="loginScreen_First_Name"></span>
                            </td>
                            <td class="fieldValuesCell">
                                <input class="fieldValuesTextField upperCaseField" id="firstNameTextField" maxlength="30" type="text" tabindex=3/>
                            </td>
                        </tr>
                        <tr class="fieldRow">
                            <td class="fieldLabelsCell fieldSize55">
                                <span data-translationKey="loginScreen_Last_Name"></span>
                            </td>
                            <td class="fieldValuesCell">
                                <input class="fieldValuesTextField upperCaseField" id="lastNameTextField" maxlength="30" type="text" tabindex=4/>
                            </td>
                        </tr>
                         <tr class="fieldRow" id="employeeNumberID_Row">
                            <td class="fieldLabelsCell fieldSize55">
                                <span data-translationKey="loginScreen_EmployeeNumberID_Label"></span>
                            </td>
                            <td class="fieldValuesCell">
                                <input class="fieldValuesTextField" id="employeeNumberIDTextField" type="tel" maxlength="9" tabindex=3/>
                            </td>
                        </tr>
                    </table>
                    <br>
                    <center>
                    <div id="signature_training_completion">
                           <br>
                           <center>
	                           <div class="signatureTitle">
	                           <span class="signatureTitleFont" data-translationKey="loginScreen_signatureBox_title"></span>
	                           </div>
                           </center>
                           <br>
							<div class="signatureContainer sugnatureFixesLogine">
								<div class="signatureBorder" id="loginScreen_SignatureContainer">
											<div class="signature" id="signatureOfStaffMember"/>
								</div>
							</div>
							<br>
							<span id="signature_Reset_Button_login_Screen" class="button grayflat sigResetButton proceedButtonWidth"> 
							         <span class="buttonText" data-translationKey="signatureScreen_Reset_Button_Label"></span>
							</span>
				    </div>			
					</center>
                    <br>

                    <!-- WICI-83 Training //-->                    
                    <center>
                        <div id="mode_slider_area">
                            <span class="training_title" data-translationKey="loginScreen_Training_title"></span>
                            <br>
                            <br>
                            <div class ="hidden">
                                <select id="mode_slider" data-role="slider">
                                    <option  id="app_mode" value="APP" selected></option>
                                    <option  id="training_mode" value="TRAINING" ></option>
                                </select>
                                <br>
                                <br>
                            </div>
                           <div class ="hidden" >
                                <select id="mode_slider_Fr" data-role="slider" >
                                    <option  id="app_mode1" value="APP" selected></option>
                                    <option  id="training_mode1" value="TRAINING" ></option>
                                </select>
                                 <br>
                                <br>
                            </div>
 						</div>
 					 </center>
                    <!-- WICI-83 Tranning //-->

                    <center>
			               <div id="OtherStaff_TitleAreaYesNO" >
	                           <table id="titleTable" class="stretchThisTable" >
	                             <tr >
	                                 <td class="fieldLabelsSingleCellYesNo">
	                                      <span data-translationKey="loginscreen_toggleTitle"></span>
	                                 </td>
	                               	  <td class="fieldValuesOneCellFlipYesNo fieldValuesCellFlipYesNo hidden">
                                            
	                                        <select id="flipStaffMember" data-role="slider">
								                        <option id="flipStaffMember_no" value="N" selected></option>
								                        <option id="flipStaffMember_yes" value="Y"></option>
							                </select> 
	                                 </td>   
                                    <td class="fieldValuesOneCellFlipYesNo fieldValuesCellFlipYesNo hidden">
                                            
	                                        <select id="flipStaffMember1" data-role="slider">
								                        <option id="flipStaffMember_no1" value="N" selected></option>
								                        <option id="flipStaffMember_yes1" value="Y"></option>
							                </select> 
	                                 </td>                  
	                            </tr>
	                          </table>
			              </div>
			              <br>
			              <table id="loginFieldsTable_OtherstaffMember" class="loginTableWidth">
		                        <tr class="fieldRow ">
		                            <td class="fieldLabelsTopCell fieldSize55">
		                                <span data-translationKey="loginScreen_First_Name"></span>
		                            </td>
		                            <td class="fieldValuesCell">
		                                <input class="fieldValuesTextField upperCaseField" id="firstNameOtherStaffMemberTextField" maxlength="30" type="text" tabindex=3/>
		                            </td>
		                        </tr>
		                        <tr class="fieldRow">
		                            <td class="fieldLabelsCell fieldSize55">
		                                <span data-translationKey="loginScreen_Last_Name"></span>
		                            </td>
		                            <td class="fieldValuesCell">
		                                <input class="fieldValuesTextField upperCaseField" id="lastNameOtherStaffMemberTextField" maxlength="30" type="text" tabindex=4/>
		                            </td>
		                        </tr>
		                         <tr class="fieldRow" id="employeeNumberID_OtherStaffMember_Row">
		                            <td class="fieldLabelsCell fieldSize55">
		                                <span data-translationKey="loginScreen_EmployeeNumberID_Label"></span>
		                            </td>
		                            <td class="fieldValuesCell">
		                                <input class="fieldValuesTextField" id="employeeNumberIDOtherStaffMemnerTextField" type="tel" maxlength="9" tabindex=2/>
		                            </td>
		                        </tr>
                        </table>
		           </center>
                </div>
            </center>
            <br>
            <center>
                <span id="loginScreen_LoginButton" class="button greenflat loginButtonWidth">
                    <span class="buttonText" data-translationKey="loginScreen_Button_Label"></span>
                </span>
                <br>
            </center>
            <center>
                <span id="loginScreen_TrainingButton" class="button greenflat loginButtonWidth">
                    <span class="buttonText" data-translationKey="loginScreen_TrainingButton"></span>
                </span>
                <br>
            </center>            
        </div>
        <br>
        <br>
        <br>
        <br>
        <br>
    </div>
</script>

<script id="WICIMobilePaymentsScreen-template" type="text/x-jquery-tmpl">
    <div class="ScreenLayout">
		<br>
		<br>
		<div class="overlay" id="mobilePaymentHandoutTabToRepDialog-container">
  			<div class="handoutTabToCustomerpopup_chooseProduct">
    			<p class="text-left" data-translationKey="mobilePayment_HandoutTabToRepDialogContent"></p>
    			<div>      				
      				<button class="dialog-btn-ok" id="mobilePaymentHandoutToRepOk" data-translationKey="mobilePayment_HandoutTabToRepDialogOk"></button>
					<button class="dialog-btn-cancel" id="mobilePaymentHandoutToRepCancel" data-translationKey="mobilePayment_HandoutTabToRepDialogCancel"></button>
    			</div>
  			</div>
		</div>	
	    <div class="mobilePaymentContentSize">
	    	<table id="mobilePhoneField"  class="stretchThisTable margingMobilePhoneField">
	        	<tr class="fieldRow">
	            	<td class="fieldEmailInfoCell fieldCellSize35">
	                	<span data-translationKey="mobilePayment_mobilePhone"></span>
						<span id="mobile_phone_div">
				          <a class="tooltip contactInfomation_i_icon margintopbottomleft" id="mobile_infomation_phone">
             		      	<span class="tooltipphonetext" data-translationKey="contactInfo_PhoneToolTipMsg"></span>
                          </a>
                        </span>
	                </td>
	                <td class="fieldValuesTopCell">
	                	<input class="fieldValuesTextField" id="mobilePayment_mobilePhone_TextField" type='tel' maxlength="10" />
	                </td>
	        	</tr>
	    	</table>
	    		<div id="mobilePayments">
				<p class="pageSubTitle marginBottom10" data-translationKey="personalData_MobilePayments" ></p>
				<p class="personalInfoText" data-translationKey="personalData_MobilePayments_Para1" ></p>
				<p class="personalInfoText" data-translationKey="personalData_MobilePayments_Para3" ></p>							
				
				<table id="consentGrantedRadioButtons" class="personalInfoAndroidApplePayTable tableNoCellsBorder">
					<tr>
						<td class="androidAppleNothankstdWidth">
						    <div class="agentRadiobutton">
	                                	<input type="radio" id="androidPay_CheckField" class="topAndroidApple" name="yesNothanksRadio" value="noThanks">
						     </div>
							<a href="#" id="androidPay" class="androidApplePay" data-lang-class>
                        		<span class="pay_Labels" data-translationKey="personalData_MobilePayments_Para4"></span>
                    		</a>
						</td>
						<td class="androidAppleNothankstdWidth">
						    <div class="agentRadiobutton">
	                                	<input type="radio" id="applePay_CheckField" class="topAndroidApple" name="yesNothanksRadio" value="noThanks">
						    </div>
							<a href="#" id="applePay" class="androidApplePay" data-lang-class>
                        		<span class="pay_Labels" data-translationKey="personalData_MobilePayments_Para5"></span>
                    		</a>
						</td>
						<td class="androidAppleNothankstdWidth">
						     <div class="agentRadiobutton">
	                                	<input type="radio" id="nothanks_CheckField" class="topAndroidApple" name="yesNothanksRadio" value="noThanks">
						     </div>
						      <a href="#" id="noThanks" class="androidApplePay radioTextWidth agentRadioButton" data-translationKey="personalData_AndroidorApplePay_NoThanks"></a>
						</td>
					</tr>
				</table>
			</div>
		</div>
        <div id="noteForMobilePayment" class="footerNoteMobilePayment">
		      <p class="personalInfoIncText" data-translationKey="personalData_TermsandConditions_Para1"></p>
          	  <p class="personalInfoIncText" data-translationKey="personalData_TermsandConditions_Para2"></p>
          	  <br>
	          <span class="phoneNote" data-translationKey="personalData_Note"></span>
	   </div>        
	
	</div>
     
</script>

<script id="WICIOptionalProductsScreen-template" type="text/x-jquery-tmpl"> 
<div class="ScreenLayout_OptionalProducts" id="OptionalProductsScreen_PageContents">
	<br> <br>

	<div class="overlay" id="useCaseThree-container">
  		<div class="handoutTabToCustomerpopup_chooseProduct">
			{{if activationItems.getModel('financialData').get('insurance_CPType_Available') == "CP_Complete"}}
    			<p class="text-left normal_text" data-translationKey="optionalProducts_UseCaseThreeDialogContentCPC"></p>
			{{else activationItems.getModel('financialData').get('insurance_CPType_Available') == "CP_LifeDisability" }}
				<p class="text-left normal_text" data-translationKey="optionalProducts_UseCaseThreeDialogContentCPLD"></p>
			{{/if}}
			<center class="retrieveAppPadding">
			<div>      				
      			<button class="dialog-btn-ok" id="useCaseThreeOk" data-translationKey="optionalProducts_UseCaseThreeDialogOk"></button>
    		</div>
			</center>
  		</div>
	</div>

	<center>
		<span id="OPClickHere" class="pageTitle"
			data-translationKey="optionalProducts_PageTitle"></span>
	</center>
	<br>
</div>

<div class="ScreenLayout_OptionalProducts" id="OptionalProductsScreen_PageContents_CPC_Title">
	<div id="title_1" class="title">
		<center>
			<div class="credit_prot_division_cpc">
				<img class="red_icon" src="app/images/red_icon.png" >
				<span data-translationKey="optionalProducts_CPC_title" class="credit_prot"></span>
			</div>
		</center>
		<div class="title_arrow_cpc">
			<img class="red_arrow" src="app/images/red_arrow.png" >
		</div>
	</div>
</div>

<br>
<div class="ScreenLayout_OptionalProducts align_center" id="OptionalProductsScreen_PageContents_CPC_Details">
	<div id="section_1" class="section wrapper_padding">
		<p class="bold" data-translationKey="optionalProducts_CPC_Helps_cover"></p>
		<ul>
			<li data-translationKey="optionalProducts_CPC_LI_Involuntary"></li>		
		 	<li data-translationKey="optionalProducts_CPC_LI_Total"></li>
		    <li data-translationKey="optionalProducts_CPC_LI_Terminal"></li>		
		</ul>
		<img src="app/images/red_separator.png" alt="Red separator" />
		<br><br>
		<p class="fontWith17" data-translationKey="optionalProducts_CPC_text_monthly"></p>		
		<h3 class="bold fontWith31" data-translationKey="optionalProducts_CPC_text_for_every" ></h3>		
		<p class="fontWith17" data-translationKey="optionalProducts_CPC_text_average"></p>		
		<h4 data-translationKey="optionalProducts_CPC_text_bill"></h4>		
		<h4 data-translationKey="optionalProducts_CPC_text_available"></h4>
		<h4 data-translationKey="optionalProducts_CPC_text_interest"></h4>
	</div>
</div>

<div class="ScreenLayout_OptionalProducts" id="OptionalProductsScreen_PageContents_CPLD_Title">
	<div id="title_1" class="title">
		<center>
			<div class="credit_prot_division">
				<img class="red_icon" src="app/images/red_icon.png" >
				<span data-translationKey="optionalProducts_CPLD_title"class="credit_prot"></span>
			</div>
		</center>
		<div class="title_arrow">
			<img class="red_arrow" src="app/images/red_arrow.png" >
		</div>
	</div>
</div>

<br>
<div class="ScreenLayout_OptionalProducts align_center" id="OptionalProductsScreen_PageContents_CPLD_Details">
	<div id="section_1" class="section wrapper_padding">
		<p class="bold" data-translationKey="optionalProducts_CPLD_Helps_cover"></p>
		<ul>
		 	<li data-translationKey="optionalProducts_CPLD_LI_Total"></li>
		    <li data-translationKey="optionalProducts_CPLD_LI_Life"></li>
		</ul>
		<img src="app/images/red_separator.png" alt="Red separator" />
		<br><br>
		<p class="fontWith17" data-translationKey="optionalProducts_CPLD_text_monthly"></p>
		<h3 class="bold fontWith31" data-translationKey="optionalProducts_CPLD_text_for_every" ></h3>		
		<p class="fontWith17" data-translationKey="optionalProducts_CPLD_text_average"></p>		
		<h4 data-translationKey="optionalProducts_CPLD_text_bill"></h4>		
		<h4 data-translationKey="optionalProducts_CPLD_text_available"></h4>
	</div>
</div>

<div class="ScreenLayout" id="OptionalProductsScreen_PageContents">
<!-- VZE-398 - New Insurance added //-->
	<div id="optionalProductsList">
		<center>
			<table class="stretchThisTable grayTableNoBorder tableWithPaddingTopAndBottom20  elementWithFontSize12" id="optionalProductsTable">
               <thead class="height0">     
                <tr>
                	<td colspan="2"class="op_fieldLabelsCell fieldLabelsCellWithTopAlign elementWithTop5">
                    	<span data-translationKey="optionalProducts_TableTitle"></span>
                    </td>
                </tr>
               </thead>
               <tbody>
					<!-- Start Credit Protector Life & Disability  -->
					<tr id="optionalProductsCreditProtectorLifeAndDisabilityItem">
						<td class="op_table_radio_td">
							<div class="wiciCheckBox">
                            	<input id="optionalProducts_CPLD_CheckField" type="checkbox" name="additionalInformation_OptionalProducts"/>
						    	<label for="optionalProducts_CPLD_CheckField"></label> 
					        </div>
				       </td>
					   <td class="op_table_title_td">
					   		<div class="section_3_copy">
					        	<div class="section_3_copy_inner">
						        	<h4 data-translationKey="OP_CPLD_title" class="elementWithBoldFont"></h4>
						            <ul>
							        	<li data-translationKey="OP_CPLD_Rule_li_1_available"></li>
						            </ul>
					             </div>
				             </div>
						<br>
					   </td>
					</tr>
                    <tr id="lineSeparatorForCPLD">
                         <td   class="paddingTopAndBottom2 op_table_radio_td"></td>
                         <td class="paddingTopAndBottom2 op_table_title_td">
							<table id="optionalProducts_SelectedProductTable" class="stretchThisTable"/>
                         </td>
                    </tr>
					<tr id="optionalProducts_CPLD_Table">
						<td class="op_table_title_td" colspan="2" id="optionalProducts_CPLD_Area">
							<div class="elementWithRightMargin15 cpc_area_bg_grey">
								<div class="privacyArea">
									<div class="paddingLeft30 op_paddingBottom10 cpc_bg_white">
										<br>
					                    <p id ="CPLD_bold"></p>
					                    <p id ="CPLD_disclose" class="bold cpc_insurance_disclosure_title"></p>
										<br>
										<div id= "QC_langSlider" class="right_Align">
											<label id="QC_langSlider" class="switch btn-color-mode-switch">
												<input id="language_slider_CPLD" style="margin-left:60px"; type="checkbox" name="color_mode"  value="1">
												<label for="color_mode" data-off="FRANÇAIS" data-on="ENGLISH" class="labelFont btn-color-mode-switch-inner labelForToggle"></label>
											</label>
								 		</div> 
								  		<br>
								  		<br>
					                    <p id ="CPLD_Coverage" class="offer_details_title_3 bold"></p>
										<p id ="CPLD_important" class="offer_details_title_2 bold"></p>
					                    <p id ="CPLD_group1"  class="withoutPadding_p"></p>
										<p id ="CPLD_group2"  class="withoutPadding_p"></p>
										<p id ="CPLD_group3" class="withoutPadding_p"></p>
										<p id ="CPLD_group4"  class="withoutPadding_p"></p>
										<p id ="CPLD_group5"  class="withoutPadding_p"></p>
					                    <p id ="CPLD_premium"  class="offer_details_title_2 bold"></p>
										<p id ="CPLD_premium_p" class="withoutPadding_p"></p>
										<p id ="CPLD_eligibility" class="offer_details_title_2 bold"></p>
										<p id ="CPLD_eligibility_p" class="withoutPadding_p"></p>
										<p id ="CPLD_benefits"  class="offer_details_title_2 bold"></p>
										<p id ="CPLD_insurer"  class="OP_WithoutPadding_p"></p>
					                    <ul class="op_style_ul_without_padding">
						                	<li id= "CPLD_insurer_para1" ></li>
						                    <li id = "CPLD_insurer_para2"></li>
					                    </ul>
					                    <p id ="CPLD_monthly_benefits_p" class="OP_WithoutPadding_p"></p>
										<ul class="op_style_ul_without_padding">
											<li id ="CPLD_monthly_benefits_20_p" ></li>
					                    	<li id ="CPLD_monthly_benefits_10_p" ></li>
										</ul>
										<p id ="CPLD_outstanding_p" class="OP_WithoutPadding_p"></p>
					                    <p id ="CPLD_initial_benefit"  class="OP_WithoutPadding_p"></p>
					                    <p id ="CPLD_all_p" class="OP_WithoutPadding_p" ></p>
										<p id ="CPLD_life_coverage"  class="offer_details_title_2 bold"></p>
					                    <p id ="CPLD_life_coverage_p1" class="withoutPadding_p"></p>
					                    <p id ="CPLD_life_coverage_p2" class="withoutPadding_p"></p>
										<p id ="CPLD_life_coverage_p3" class="withoutPadding_p"></p>
										<p id ="CPLD_claim_procedure"  class="offer_details_title_2 bold"></p>
					                    <p id ="CPLD_claim_procedure_p1"  class="withoutPadding_p"></p>
					                    <p id ="CPLD_claim_procedure_p2"  class="withoutPadding_p"></p>
										<p id ="CPLD_claim_procedure_p3"  class="withoutPadding_p"></p>
										<p id ="CPLD_termination" class="offer_details_title_2 bold"></p>
					                    <p id ="CPLD_termination_p1" class="withoutPadding_p"></p>
					                    <p id ="CPLD_termination_p2" class="withoutPadding_p"></p>
					                    <ul class="op_style_ul_without_padding">
						                	<li id ="CPLD_termination_l1"></li>
						                    <li id ="CPLD_termination_l2"></li>
						                    <li id ="CPLD_termination_l3"></li>
						                    <li id ="CPLD_termination_l4"></li>
						                    <li id ="CPLD_termination_l5"></li>
						                    <li id ="CPLD_termination_l6"></li>
					                    </ul>
										<p id="CPLD_creditor"  class="offer_details_title_2 bold"></p>
					                    <p id="CPLD_creditor_p1"  class="withoutPadding_p"></p>
										<p id="CPLD_Enrolling"  class="offer_details_title_3 bold withoutPadding_p"></p>
					                    <ul class="op_style_ul_without_padding op_paddingLeft30">
						                	<li id="CPLD_Enrolling_1" ></li>
						                    <li id="CPLD_Enrolling_2" ></li>
						                    <li id="CPLD_Enrolling_3" ></li>
						                    <li id="CPLD_Enrolling_4" ></li>
						                    <li id="CPLD_Enrolling_5" ></li>
						                    <li id="CPLD_Enrolling_6" ></li>
						                    <li id="CPLD_Enrolling_7" ></li>
						                    <li id="CPLD_Enrolling_8" ></li>
						                    <li id="CPLD_Enrolling_9" ></li>
						                    <li id="CPLD_Enrolling_10" ></li>
						                    <li id="CPLD_Enrolling_11" ></li>
						                    <li id="CPLD_Enrolling_12" ></li>
						                    <li id="CPLD_Enrolling_13" ></li>
					                    </ul>
										<br>
										<p id="CPLD_liketoenroll"  class="offer_details_title_3 bold"></p>
										<p id="CPLD_consentyesorno" class="offer_details_title_3 bold"></p>
										<br>
									</div>
									<br><br>
                            		<div class="warningDIV" id="warningDIVCPLD" name="warningDIVCPLD">
									<center>
										<p class="warningHeader" id="warningHeaderCPLD" name="warningHeaderCPLD"><span data-translationKey="optionalProducts_CPLD_WarningHeader"/></p>
									</center>
									<div class="warningText signatureCredentialsContainer" id="warningTextCPLD" name="warningTextCPLD">
										<p><span data-translationKey="optionalProducts_SignatureAgreement_CPLD_subTitle" /></p>
										<p><span data-translationKey="optionalProducts_CPLDProduct" /></p>
									
										<div class="agreementBoxContainerEX" style="display: table" id="optionalProduct_CPLD_AcceptBox_Area">
											<div class="agreementBoxContainerEX" style="display: table" id="optionalProduct_CPLD_AcceptBox_Area1">
												<div class="rightContaier op_padding">
													<div class="wiciCheckBox">
														<input type="checkbox" id="optionalProducts_CPLD_Agreement">
														<label for="optionalProducts_CPLD_Agreement"></label>
													</div>
												</div>
												<div class="leftContainer opCheckBoxLabel_CLD" style="display: table-cell; vertical-align: middle;">
													<div class="warningText1" id="warningText1CPLD" name="warningText1CPLD">
														<p data-translationKey="optionalProducts_TermsAndConditions_16_CPLD" class="offer_details_title_4 bold lineHeight"></p>							 	        	         
													</div>	
													<div class="warningText1" id="warningText1CPLD_QC" name="warningText1CPLD_QC">
														<p data-translationKey="optionalProducts_TermsAndConditions_16_CPLD_QC" class="offer_details_title_4 bold lineHeight"></p>							 	        	         
													</div>									         
												</div>
											</div>
											<div class="agreementBoxContainerEX marginTop5" style="display: table" id="optionalProduct_CPLD_AcceptBox_Area2">
												<div class="rightContaier op_padding">
													<div class="wiciCheckBox">
														<input type="checkbox" id="optionalProducts_CPLD_Agreement1">
														<label for="optionalProducts_CPLD_Agreement1"></label>
													</div>
												</div>
												<div class="leftContainer opCheckBoxLabel_CLD" style="display: table-cell; vertical-align: middle;">
													<div class="warningText2 marginTop5" id="warningText2CPLD" name="warningText2CPLD">
														<p data-translationKey="optionalProducts_TermsAndConditions_16_CPLD_1" class="offer_details_title_4 bold lineHeight"></p>
							 	        	         	<br>
														<p data-translationKey="optionalProducts_TermsAndConditions_16_CPLD_2" class="offer_details_title_4 bold lineHeight"></p>
													</div>
										         	<div class="warningText2 marginTop5" id="warningText2CPLD_QC" name="warningText2CPLD">
												    	<p data-translationKey="optionalProducts_TermsAndConditions_CPLD_QC_1" class="offer_details_title_4 bold lineHeight"></p>
														<br>
												    	<p data-translationKey="optionalProducts_TermsAndConditions_CPLD_QC_2" class="offer_details_title_4 bold lineHeight"></p>
									    		    	<br>
												    	<p data-translationKey="optionalProducts_TermsAndConditions_CPLD_QC_3" class="offer_details_title_4 bold lineHeight"></p>
												    	<br>
											     		<p data-translationKey="optionalProducts_TermsAndConditions_CPLD_QC_4" class="offer_details_title_4 bold lineHeight"></p>
													</div>
												</div>
											</div>
										</div>										
									</div>
									<br>
									<center>
										<div class="signatureContainer sugnatureFixes">
										<div class="signatureBorder" id="optionalProductsScreen_CPLD_SingnatureContainer">
											<div class="signature" id="signature_CPLD"/></div>
										</div>
										<br> 
										<span id="signature_Reset_Button1" class="button grayflat sigResetButton proceedButtonWidth"> <span class="buttonText" data-translationKey="signatureScreen_Reset_Button_Label"></span></span>
									</center>
								</div>
                            <br><br>
							</td>
						</tr>
					</div>
					<!-- End Credit Protector Life & Disability  -->

					<!-- Start Credit Protector Complete    -->
					<div>
						<tr id="optionalProductsCreditProtectorCompleteItem">
							<td class=" op_table_radio_td">
                            	<div class="wiciCheckBox">
							 		<input id="optionalProducts_CPC_YesNoTextField" type="checkbox" name="additionalInformation_OptionalProducts" />
                             		<label for="optionalProducts_CPC_YesNoTextField"></label>
                            	</div>
							</td>
							<td class="op_table_title_td">
								<div class="section_3_copy">
					            	<div class="section_3_copy_inner">
						            	<h4 data-translationKey="OP_CPC_title"></h4>
						            	<ul>
							         		<li data-translationKey="OP_CPC_Li_1_available"></li>
						            	</ul>
					                </div>
				                </div>
							</td>
						</tr>
						<tr  class="fieldRow">
                  		<tr id="lineSeparatorForCPC">
                     		<td class="paddingTopAndBottom2 op_table_radio_td">
							</td>
                     		<td class="paddingTopAndBottom2 op_table_title_td">
								<table id="optionalProducts_SelectedProductTable" class="stretchThisTable"/>
                     		</td>
                  		</tr>
						<tr id="optionalProducts_CPC_Table">
							<td class="op_table_title_td" id="optionalProducts_CPC_Area" colspan="2">
								<div class="elementWithRightMargin15 cpc_area_bg_grey">
									<div class="privacyArea">
										<div class="paddingLeft30 op_paddingBottom10 cpc_bg_white">
										     <br>
					                         <p id="CPC_QC1" ></p>
                                             <p  id="CPC_QC2"  class="bold cpc_insurance_disclosure_title"></p>
											 <br>
										 	 <div id="languageSlider_CPC_QC" class="right_Align">
												<label class="switch btn-color-mode-switch labelForToggle">
													<input  style="margin-left:60px"; id="language_slider_CPC_QC"  type="checkbox" name="color_mode"  value="1">
													<label for="color_mode" data-off="FRANÇAIS" data-on="ENGLISH" class="labelForToggle labelFont btn-color-mode-switch-inner"></label>
												</label>
											 </div> 
											<br>
											<br>
											<p id="CPC_QC3"  class="offer_details_title_3 bold"></p>
											<p id="CPC_QC4"  class="offer_details_title_2 bold"></p>
											<p id="CPC_QC5"  class="withoutPadding_p"></p>
											<p id="CPC_QC6"  class="withoutPadding_p"></p>
											<p id="CPC_QC7"  class="withoutPadding_p"></p>
											<p id="CPC_QC8"  class="withoutPadding_p"></p>
											<p id="CPC_QC9"  class="withoutPadding_p"></p>
											<p id="CPC_QC10"  class="offer_details_title_2 bold"></p>
											<p id="CPC_QC11"  class="withoutPadding_p"></p>
											<p id="CPC_QC12"  class="offer_details_title_2 bold"></p>
											<p id="CPC_QC13"  class="withoutPadding_p"></p>
											<p id="CPC_QC14"  class="offer_details_title_2 bold"></p>
											<p id="CPC_QC15"  class="withoutPadding_p"></p>
											<p id="CPC_QC16"  class="offer_details_title_3 bold op_paddingLeft10"></p>
											<p id="CPC_QC17"  class="OP_WithoutPadding_p op_paddingLeft27"></p>
											<ul class="op_style_ul_without_padding">
												<li id="CPC_QC18" ></li>
												<li id="CPC_QC19" ></li>
												<li id="CPC_QC20" ></li>
											</ul class="style_ul_without_padding">
											<p id="CPC_QC21"  class="offer_details_title_2 bold op_paddingLeft26"></p>
											<p  id="CPC_QC22"  class="offer_details_title_3 bold op_paddingLeft10"></p>
											<p id="CPC_QC23"  class="OP_WithoutPadding_p op_paddingLeft27"></p>
											<ul class="op_style_ul_without_padding">
												<li id="CPC_QC24" ></li>
 												<li id="CPC_QC25" ></li>
												<li id="CPC_QC26" ></li>
											</ul>
											<p id="CPC_QC27"  class="OP_WithoutPadding_p"></p>
											<ul class="op_style_ul_without_padding" >
												<li id="CPC_QC28" ></li>
												<li id="CPC_QC29" ></li>
											</ul>
											<p id="CPC_QC30"  class="OP_WithoutPadding_p"></p>
											<p id="CPC_QC31"   class="OP_WithoutPadding_p"></p>
											<p id="CPC_QC32"  class="OP_WithoutPadding_p" ></p>
											<p id="CPC_QC33"  class="OP_WithoutPadding_p"></p>
											<p id="CPC_QC34"  class="offer_details_title_2 bold"></p>
 											<p id="CPC_QC35" class="OP_WithoutPadding_p"></p>
											<ul class="op_style_ul_without_padding">
												<li id="CPC_QC36" ></li>
												<li id="CPC_QC37" ></li>
												<li id="CPC_QC38" ></li>
											</ul>
											<p id="CPC_QC39"  class="OP_WithoutPadding_p"></p>
											<ul class="op_style_ul_without_padding" >
											    <li id="CPC_QC40" ></li>
												<li id="CPC_QC41" ></li>
											</ul>
											<p id="CPC_QC42"  class="OP_WithoutPadding_p"></p>
											<p id="CPC_QC43"   class="OP_WithoutPadding_p"></p>
											<p id="CPC_QC44"  class="OP_WithoutPadding_p" ></p>
											<p id="CPC_QC45"  class="offer_details_title_2 bold"></p>
											<p id="CPC_QC46"  class="withoutPadding_p"></p>
											<p id="CPC_QC47"  class="withoutPadding_p"></p>
											<p id="CPC_QC48"  class="withoutPadding_p"></p>
											<p id="CPC_QC49"  class="offer_details_title_2 bold"></p>
											<p id="CPC_QC50"  class="withoutPadding_p"></p>
											<p id="CPC_QC51"  class="withoutPadding_p"></p>
											<p id="CPC_QC52"  class="withoutPadding_p"></p>
											<p id="CPC_QC53"  class="offer_details_title_2 bold"></p>
											<p id="CPC_QC54"  class="withoutPadding_p"></p>
											<p id="CPC_QC55"  class="withoutPadding_p"></p>
 											<ul class="op_style_ul_without_padding">
												<li id="CPC_QC56" ></li>
												<li id="CPC_QC57" ></li>
 												<li id="CPC_QC58" ></li>
												<li id="CPC_QC59" ></li>
												<li id="CPC_QC60" ></li>
												<li id="CPC_QC61" ></li>
											</ul>
											<p id="CPC_QC62"  class="offer_details_title_2 bold"></p>
											<p id="CPC_QC63"   class="withoutPadding_p"></p>
											<p id="CPC_QC64"  class="offer_details_title_3 bold withoutPadding_p"></p>
											<ul class="op_style_ul_without_padding op_paddingLeft30">
											<li id="CPC_QC65" ></li>
											<li id="CPC_QC66" ></li>
											<li id="CPC_QC67" ></li>
											<li id="CPC_QC68" ></li>
											<li id="CPC_QC69" ></li>
											<li id="CPC_QC70" ></li>
											<li id="CPC_QC71" ></li>
											<li id="CPC_QC72" ></li>
											<li id="CPC_QC73" ></li>
											<li id="CPC_QC74" ></li>
											<li id="CPC_QC75" ></li>
											<li id="CPC_QC76" ></li>
											<li id="CPC_QC77" ></li>
											</ul>
											<br>
											<p id="CPC_QC78"  class="offer_details_title_3 bold"></p>
											<p id="CPC_QC79"  class="offer_details_title_3 bold"></p>
											</div>
											<br><br>
                                            <div class="warningDIV" id="warningDIVCPC" name="warningDIVCPC">
												<center>
													<p class="warningHeader" id="warningHeaderCPC" name="warningHeaderCPC"><span data-translationKey="optionalProducts_CPC_WarningHeader"/></p>
												</center>
												<div class="warningText signatureCredentialsContainer" id="warningTextCPC" name="warningTextCPC">
													<p><span data-translationKey="optionalProducts_SignatureAgreement_CPC_subTitle"/></p>
													<p><span class="elementWithTop17 elementWithBoldFont" data-translationKey="optionalProducts_CPCProducts"/></p>
                                            	
                                            		<div class="agreementBoxContainerEX" style="display: table" id="optionalProducts_CPC_AcceptBox_Area">
														<div class="agreementBoxContainerEX" style="display: table" id="optionalProducts_CPC_AcceptBox_Area1">
															<div class="rightContaier op_padding">
																<div class="wiciCheckBox">
																	<input type="checkbox" id="optionalProducts_CPC_Agreement"> 
                                                        			<label for="optionalProducts_CPC_Agreement"></label>
												    			</div>
											    			</div>
											    			<div class="leftContainer opCheckBoxLabel_CLD" style="display: table-cell; vertical-align: middle;">
																<div class="warningText1" id="warningText1CPC" name="warningText1CPC">
													 				<p data-translationKey="optionalProducts_TermsAndConditions_16_CPC" class="offer_details_title_4 bold lineHeight"></p>																
											    				</div>
																<div class="warningText1" id="warningText1CPC_QC" name="warningText1CPC_QC">
													 				<p data-translationKey="optionalProducts_TermsAndConditions_16_CPC_QC" class="offer_details_title_4 bold lineHeight"></p>																
											    				</div>																
															</div>
														</div>
														<div class="agreementBoxContainerEX marginTop5" style="display: table" id="optionalProducts_CPC_AcceptBox_Area2">
															<div class="rightContaier op_padding">
																<div class="wiciCheckBox">
																	<input type="checkbox" id="optionalProducts_CPC_Agreement1"> 
                                                        			<label for="optionalProducts_CPC_Agreement1"></label>
												    			</div>
											    			</div>
											    			<div class="leftContainer opCheckBoxLabel_CLD" style="display: table-cell; vertical-align: middle;">
																<div class="warningText2 marginTop5" id="warningText2CPC" name="warningText2CPC">
													 				<p data-translationKey="optionalProducts_TermsAndConditions_16_CPC_1" class="offer_details_title_4 bold lineHeight"></p>
																	<br>
													 				<p data-translationKey="optionalProducts_TermsAndConditions_16_CPC_2" class="offer_details_title_4 bold lineHeight"></p>
											    				</div>
																<div class="warningText2 marginTop5" id="warningText2CPC_QC" name="warningText2CPC">
													 				<p data-translationKey="optionalProducts_TermsAndConditions_CPC_QC_1" class="offer_details_title_4 bold lineHeight"></p>
																	<br>
													 				<p data-translationKey="optionalProducts_TermsAndConditions_CPC_QC_2" class="offer_details_title_4 bold lineHeight"></p>
																	<br>
													 				<p data-translationKey="optionalProducts_TermsAndConditions_CPC_QC_3" class="offer_details_title_4 bold lineHeight"></p>
																	<br>
													 				<p data-translationKey="optionalProducts_TermsAndConditions_CPC_QC_4" class="offer_details_title_4 bold lineHeight"></p>
											    				</div>
															</div>
														</div>														
													</div>
												</div>
												<br>
												<center>
													<div class="signatureContainer sugnatureFixes">
														<div class="signatureBorder" id="optionalProductsScreen_CPC_SignatureContainer">
															<div class="signature" id="signature_CPC" />
														</div>
													</div>
													<br> 
													<span id="signature_Reset_Button2" class="button grayflat sigResetButton proceedButtonWidth"> 
														<span class="buttonText" data-translationKey="signatureScreen_Reset_Button_Label"></span>
													</span>
												</center>
										</div>
                                        <br><br>
							</td>
						</tr>
					<br>
					</div>
					<!-- End Credit Protector Complete   -->

					<!-- Start No Do not Enrol me -->
					<div id="optionalProductsNACheckFieldItem">
						<tr>
							<td
								class="op_table_radio_td">
								 <div class="wiciCheckBox marginTop23">
								         <input id="optionalProducts_NA_CheckField" type="checkbox"
								        name="additionalInformation_OptionalProducts"/>
 								    <label for="optionalProducts_NA_CheckField"></label>
 								</div>
							</td>
							<td class="op_table_title_td">
							 <div class="section_3_copy">
					                    <div class="section_3_copy_inner">
							                  <h4 class="elementWithBoldFont marginTopMinus3"
								                 data-translationKey="optionalProducts_DoNotEnrolMe"></h4>
							            </div>
							      </div>	
							</td>
						</tr>
					</div>
			</tbody>
			<tfoot class="height0">
			</tfoot>
			</table>
		</center>
	</div>
		

	<!-- End No Do not Enrol me -->
	<br>
	<br>
	<!-- Radio button optional products ends -->
	
			<footer id="op_cp_footer_termsandconditions_cpc" class="op_wrapper_footer">
		    	<p data-translationkey="OP_CPC_FT_there_are_op_offfers" class="withoutPadding_p"></p><br>
		        <p data-translationkey="OP_CPC_FT_business_product" class="withoutPadding_p"></p><br>
		        <p data-translationkey="OP_CPC_FT_unless" class="withoutPadding_p"></p><br>
		        <p data-translationkey="OP_CPC_FT_CP" class="withoutPadding_p"></p><br>
				<p data-translationkey="OP_CPC_FT_license" class="withoutPadding_p"></p><br>
		        <p data-translationkey="OP_CPC_FT_assurant" class="withoutPadding_p"></p><br>
		        <p data-translationkey="OP_CPC_FT_mastercard" class="withoutPadding_p"></p><br>
				<p data-translationkey="OP_CPC_FT_TCPC" class="withoutPadding_p"></p>
	        </footer>

			<footer id="op_cp_footer_termsandconditions_cpld" class="op_wrapper_footer">
		    	<p data-translationkey="OP_CPLD_FT_there_are_op_offfers" class="withoutPadding_p"></p><br>
		        <p data-translationkey="OP_CPLD_FT_business_product" class="withoutPadding_p"></p><br>
		        <p data-translationkey="OP_CPLD_FT_unless" class="withoutPadding_p"></p><br>
		        <p data-translationkey="OP_CPLD_FT_CP" class="withoutPadding_p"></p><br>
				<p data-translationkey="OP_CPLD_FT_license" class="withoutPadding_p"></p><br>
		        <p data-translationkey="OP_CPLD_FT_assurant" class="withoutPadding_p"></p><br>
		        <p data-translationkey="OP_CPLD_FT_mastercard" class="withoutPadding_p"></p><br>
				<p data-translationkey="OP_CPLD_FT_TCPC" class="withoutPadding_p"></p>
	        </footer>
	<br>
	<center>
		<span id="optionalProducts_ProceedButton"
			class="button greenflat proceedToConfirmationButtonWidth"> <span
			class="buttonText" data-translationKey="optionalProducts_Proceed"></span>
		</span>
	</center>
	<br>
	<br>
</div>
</script>

<script id="WICIPendingScreen-template" type="text/x-jquery-tmpl">
    <div class="ScreenLayout" id="pendingScreen_PageContents">
	<center>
	<div id="pendingScreen_DisplayArea">
	{{if pendingScreenState==="INSESSION" || pendingScreenState==="RETRIEVEPEND_START"}}
		<div class="pageHeaderPadding">
			
        	<span class="pageTitle" data-translationKey="pendingScreen_ThankYou"></span>
        	
        	<!-- <span class="summaryPageSubTitle" data-translationKey="${cardName}"></span> //-->
        	
        	{{if activationItems.getModel('chooseProductModel').get('productCard') === 'OMR' }}
				<span class="summaryPageSubTitle" data-translationKey="signature_CashAdvantageMasterCard" ></span>
			{{else activationItems.getModel('chooseProductModel').get('productCard') === 'OMP' }}
				<span class="summaryPageSubTitle" data-translationKey="signature_GasAdvantageMasterCard" ></span>
		    {{else activationItems.getModel('chooseProductModel').get('productCard') === 'OMZ' }}
				<span class="summaryPageSubTitle" data-translationKey="signature_World_ELiteMasterCard" ></span>		
			{{else}}
				<span class="summaryPageSubTitle" data-translationKey="signature_OptionsMasterCard" ></span>
			{{/if}}
        	
         	<br>
			<br>
			<table class="fieldCellSize60" id="pendingScreen_tokenInfo">
				<tr>
					<td class="fieldLabelsSingleCell fieldCellSize60">
						<span class="fieldLabelsTitle" data-translationKey="pendingScreen_TokenLabel"></span>						
					</td>
					<td class="fieldValuesOneCell fieldCellSize60">
						<span class='uppercase'> ${token} </span>
					</td>
				</tr>
			</table>
			<br>
			<br>
			
			<div data-translationKey="pendingScreen_ParaBlock_PleaseWait"></div>
			<div id="pendingScreen_StaticStatusMessage" data-translationKey="pendingScreen_Parablock_We_are_checking" ></div>

			<!-- Start -->
			<!--
				<div id="pendingScreen_DynamicStatusMessage"> <span data-translationKey="pendingScreen_CheckXXSeconds_Text1"></span><span id="pendingScreen_pollDelay">60</span><span data-translationKey="pendingScreen_CheckXXSeconds_Text2"></span> <span id="pendingScreen_AttemptNumber">1</span> <span data-translationKey="pendingScreen_OfAttemptYY_Text1"></span><span id="pendingScreen_maxAttempts">15</span><span data-translationKey="pendingScreen_OfAttemptYY_Text2"></span></div>
			-->	
			<!-- End -->
					
			<br>
			<br>
			
			<!-- US4084 -->
			<div class="wrapper">
			  <div class="first">0 min.</div>
			  <div class="second" id="progressbar"></div>
			  <div class="third">5 mins.</div>
			</div>			
			<center><span class="marginTopPlus15" id="progress" data-translationKey="pendingScreen_PollingInProgress_Text" /></center>
			<center><span class="marginTopPlus15" id="completed" data-translationKey="pendingScreen_PollingCompleted_Text" /></center>
			
			<div data-translationKey="pendingScreen_ParaBlock_IfYouShop"></div>
			
			
			<div class="elementWithFontSize10" data-translationKey="pendingScreen_Label_RefGood30mins"></div>

        </div>

	    <span id="pendingScreen_PrintButton" class="button greenflat pendingScreen_PrintButtonSize">
	    	<div class="buttonText" data-translationKey="pendingScreen_PrintButtonLabel"></div>
		</span>

		<span data-translationKey="pendingScreen_ParaBlock_IfUnable"></span>
		<span data-translationKey="pendingScreen_ParaBlock_NoteDays"></span>

		<span id="pendingScreen_EmailButton" class="button greenflat pendingScreen_EmailButtonSize">
	    	<span class="buttonText" data-translationKey="pendingScreen_EmailButtonLabel"></span>
		</span>	
	{{/if}}	
	{{if pendingScreenState==="RETRIEVEPEND"}}
		<div class="pageHeaderPadding">
			<br>
			<br>
			<table class="fieldCellSize80" id="pendingScreen_tokenInfo">
				<tr class="fieldRow">
					<td class="fieldLabelsTopCell fieldCellSize50">
						<span class="fieldLabelsTitle" data-translationKey="pendingScreen_TokenLabel"></span>
					</td>
					<td class="fieldValuesTopCell fieldCellSize50">
						<input class="fieldValuesTextField uppercase" id="pendingScreenReferenceField" type="text" />
					</td>
				</tr>
				<tr class="fieldRow">
					<td class="fieldLabelsBottomCell fieldCellSize50">
						<span class="fieldLabelsTitle" data-translationKey="pendingScreen_PhoneNumberLabel"></span>
					</td>
					<td class="fieldValuesBottomCell fieldCellSize50">
						<input class="fieldValuesTextField" id="pendingScreenPhoneNumberField" type="tel" maxlength="10" />
					</td>
				</tr>
			</table>
			<br>
			<br>
        </div>
		<span id="checkApplicationStatusButton" class="button greenflat">
	    	<span class="buttonText" data-translationKey="pendingScreen_CheckAppStatusLabel"></span>
		</span>
	{{/if}}	
	</div>
	</center>
	</div>
</script>

<script id="WICIPersonalDataScreen-template" type="text/x-jquery-tmpl">
<!--
	personalData_CTMField								    :	"",
	personalData_CTMNumber_TextField						:	"",
	personalData_CTMAccountText	                            :   "",
	//-->
	<div class="ScreenLayout" id="PersonalDataScreen_PageContents">
	    <br>
		<br>
		<div class="overlay" id="idExpiryDateErrorMessageDialog-container">
  			<div class="handoutTabToCustomerpopup_chooseProduct">
	            <center><p class="text-center normal_text" data-translationKey="expiryDate_error_title"></p></center>
    			<p class="text-center normal_text" data-translationKey="expiryDate_error_message"></p>
    			<div>      				
      				<button class="dialog-btn-ok upperCaseField" id="idExpiryDateContinueButton" data-translationKey="expiryDate_continue_button"></button>
					<button class="dialog-btn-cancel upperCaseField" id="idExpiryDateCancelButton" data-translationKey="expiryDate_cancel_button"></button>
    			</div>
  			</div>
		</div>
		<center><span class="pageTitle" data-translationkey="personalData_TellUsAboutYourself"></span></center>
		<center>
				<div id="personalData_MyCTMArea">
			<br> <br>
			<table id="myCTMTable" class="stretchThisTable" style="border-radius: 11px 11px 0 0;">
				<tbody>
					<tr class="fieldRow">
						<td style="border-radius: 11px 0 0 11px;" class="fieldLabelsTopCell fieldCellSize35 selectedBreadcrumb">
							<span data-translationkey="personalData_CTMField"></span>
						</td>
						<td class="fieldValuesTopCell"
							style="width: 83px; border-right: none; padding-right: 0;"><input
							class="fieldValuesTextField" autocomplete="off" autocorrect="off"
							autocapitalize="off" spellcheck="false"
							id="personalData_CTMNumber_TextField_Prefix" type="tel" maxlength="6" ></td>
						<td class="fieldValuesTopCell" style="border-left: none; padding-left: 0;"><input
							class="fieldValuesTextField" autocomplete="off" autocorrect="off"
							autocapitalize="off" spellcheck="false"
							id="personalData_CTMNumber_TextField" maxlength="10"
							tabindex=1 type='tel' max="9999999999" min="0"></td>
					</tr>
				</tbody>
			</table>
			<table id="myCTMTable" class="stretchThisTable" style="border-radius: 0 0 11px 11px;">
				<tbody>
					<tr class="fieldRow">
						<td
							style="padding: 15px 8px;"
							class="fieldLabelsBottomCell fieldCellSize35 selectedBreadcrumb">
							<span id="personalData_ScanLoyaltyButton"
							class="button greenflat scanLoyaltyButtonWidth" style="padding:1em .2em 1em;">
								<span class="buttonText" style="font-size: 14pt;"
								data-translationkey="personalData_Scan_Loyalty_Label"></span>
						</span>
						</td>
						<td class="fieldValuesCell">
							<p>
								<span data-translationkey="personalData_CTMAccountText"></span>
							</p>
						</td>
					</tr>
				</tbody>
			</table>
		</div>
        </center>
        <br>
        <br>
        
 
      <div>
			<p class="fontSize25 bold paragraphAlignmentHeading4"><span data-translationKey="personalData_ScanTitle"></span></p>
			<p class="fontSize20 paragraphAlignmentStartPoint5"><span data-translationKey="PersonalData_ScanText"></span></p>
                <br>
		<center>
                <table class="stretchThisTable">
                    <tr class="fieldRow">
	                    <td class="fieldLabelsTopCell fieldCellSize33">
	                        <span data-translationKey="personalData_PlaceOfIssue"></span>
	                    </td>
	                    <td class="fieldValuesTopCell">
	                        <!-- input class="fieldValuesTextField" id="personalData_PlaceOfIssue_TextField" type="text"/-->
	                        <select class="fieldValuesTextField" id="personalData_PlaceOfIssue_TextField" tabindex=2>
							</select>
	                    </td>
	                </tr>
	                <tr class="fieldRow">
	                    <td class="fieldLabelsBottomCell fieldCellSize33">
	                        <span data-translationKey="personalData_IDType"></span>
	                    </td>
	                    <td class="fieldValuesCell">
	                        <!--input class="fieldValuesTextField" id="personalData_IDType_TextField" type="text"/-->
							<select class="fieldValuesTextField" id="personalData_IDType_TextField" type="text" tabindex=3>
							</select>
	                    </td>
	                </tr>
                </table>
				<br>
             <span id="personalData_ScanIdButton" class="personalInfo_CanadaPostButton greenColor" style="color:#ffffff;">
                <span data-translationKey="personalData_ScanID"></span>
			</span>
             <span id="personalData_NoScanIdButton" class="personalInfo_CanadaPostButton darkgrayflat scanDisabled hidden" style="color:#ffffff;" disabled>
                <span data-translationKey="ScanID_Not_Available"></span>
			</span>
		</center>

       </div>
      
        
        <br>
        <center>
			<div id="personalData_AddressLookupArea" >
	            <table id="addressLookupTable" class="stretchThisTable">
					
	                <tr class="fieldRow">
	                    <td id="personalData_IDNumber" class="fieldLabelsSingleCell fieldCellSize33">
	                        <span data-translationKey="personalData_IDNumber"></span>
	                    </td>
	                    <td class="fieldValuesCell">
	                        <input class="fieldValuesTextField upperCaseField" id="personalData_IDNumber_TextField" type="text" maxlength="20" tabindex=4/>
	                    </td>
	                </tr>
	                
	                <tr id="personalData_ExpiryDate_for_QC_healthCard" class="fieldRow hideElement">
	                    <td id="personalData_ExpiryDate" class="fieldLabelsBottomCell fieldCellSize33">
	                        <span data-translationKey="personalData_ExpiryDate"></span>
	                    </td>
	                    <td class="fieldValuesCell">
	                        <!--input class="fieldValuesTextField" id="personalData_IDType_TextField" type="text"/-->
	                        <table width="60%">
	                        <tr class="class="fieldRow"">
	                        <td class="fieldCellSize33" id="error_for_expirymonth">
							<select class="fieldValuesTextField" id="personalData_month_of_expirydate" type="text" tabindex=3>
							</select>
	                        </td>
	                        <td class="fieldCellSize33" id="error_for_expiryyear">
	                        <!--input class="fieldValuesTextField" id="personalData_IDType_TextField" type="text"/-->
							<select class="fieldValuesTextField" id="personalData_year_of_expirydate" type="text" tabindex=4>
							</select>
							</td>
							</tr>
							</table>
	                    </td>
	                </tr>                              
	                     <!-- US4365 -->
	                <tr id="personalData_ExpiryDate_Area" class="fieldRow">
	                    <td id="personalData_ExpiryDate" class="fieldLabelsBottomCell fieldCellSize33">
	                        <span data-translationKey="personalData_ExpiryDate"></span>
	                    </td>
	                    <td class="fieldValuesBottomCell">
	                      <!--  <input class="fieldValuesTextField" id="personalData_ExpiryDate_TextField" type="date" tabindex=5></input> -->
                          <div class="row stretchThisTable">
                                <div class="column_one androidPaytdWidth">
                                      <select id="expiryDate_year" class="fieldValuesTextField expirydateBackground" tabindex=5>Year</select>
					            </div>
					            <div class="column_one androidPaytdWidth">
                                      <select id="expiryDate_month" class="fieldValuesTextField expirydateBackground" tabindex=6>Month</select>
					            </div>
					            <div class="column_one androidPaytdWidth">
                                      <select id="expiryDate_day" class="fieldValuesTextField expirydateBackground" tabindex=7>Day</select>
					            </div>
					     </div>
	                    </td>
	                </tr>
	                
	            </table>
			</div>
		</center>
		<br>
		<br>
	<center>
			<div id="personalData_PersonalInfoArea" >
	            <table id="personalInfoTable" class="stretchThisTable">
	                <tr class="fieldRow">
	                    <td class="fieldLabelsTopCell fieldCellSize33">
	                        <span data-translationKey="personalData_FirstName"></span>
	                    </td>
	                    <td class="fieldValuesTopCell">
	                        <input class="fieldValuesTextField upperCaseField" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false" id="personalData_FirstName_TextField" type="text" maxlength="40" tabindex=8/>
	                    </td>
	                </tr>
	                <tr class="fieldRow">
	                    <td class="fieldLabelsCell fieldCellSize33">
	                        <span data-translationKey="personalData_Initial"></span>
	                    </td>
	                    <td class="fieldValuesCell">
	                        <input class="fieldValuesTextField upperCaseField" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false" maxlength="1" id="personalData_Initial_TextField" type="text" tabindex=9/>
	                    </td>
	                </tr>
	                <tr class="fieldRow">
	                    <td class="fieldLabelsCell fieldCellSize33">
	                        <span data-translationKey="personalData_LastName"></span>
	                    </td>
	                    <td class="fieldValuesCell">
	                        <input class="fieldValuesTextField upperCaseField" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false" id="personalData_LastName_TextField" type="text" minlength="2" maxlength="40" tabindex=10/>
	                    </td>
	                </tr>
	                <tr class="fieldRow">
	                    <td class="fieldLabelsBottomCell fieldCellSize33">
	                        <span data-translationKey="personalData_DateOfBirth"></span>
	                    </td>
	                    <td class="fieldValuesBottomCell">
	                        <!--<input class="fieldValuesTextField" id="personalData_DateOfBirth_TextField" data-role='datebox' data-options='{"mode":"datebox", "useNewStyle":false, "dialogEnable":true, "dialogForce":true}' tabindex=9/> -->
	                        <input class="fieldValuesTextField" id="personalData_DateOfBirth_TextField" min="1800-01-01" max="3000-12-31" type="date" tabindex=11></input>
	                    </td>
	                </tr>
	            </table>
			</div>
		</center>
		<br>
		<br>

	<!--
	personalData_AddressInformation						:	"ADDRESS INFORMATION",
	personalData_Address_AddressLine1					:	"Address Line 1",
	personalData_Address_AddressLine2					:	"Address Line 2",
	personalData_Address_SuiteUnit						:	"Suite/Unit",
	personalData_Address_City							:	"City",
	personalData_Address_Province						:	"Province",
		//-->

		<div class="row">
            <div class="column elementWithBoldFont">
              <!-- VZE-265 -->
               <span id="i_icon_current_mailling_address" class="bold fontSize30 currentMailingAddress" data-translationKey="personalData_current_Mailing_address"></span>
            </div>
            <div class="column_one">
        
                <span>
						<a class="tooltip personalInfomation_i_icon" id="personalInfomation_infomation_cityName">
             				<span class="tooltipCityNametext" data-translationKey="personalData_cityNameLongerThen24CharError_msg"></span>
                		</a>
               </span><br>
            </div>
        </div>
        
        <div class="row">
             <div class="column_one">
                <center>
                  <div id="canadaPostLogo" class="personalInfo_CanadianPostLogo"></div>
                </center>
              </div>
              <div class="column_two">
                   <input id="streetAddress" placeholder="Start typing a street address or postal code" type="text" class="streetAddressCanadaPost upperCaseField" tabindex=12 >
              </div>
        </div>
        <br>
         <div id="personalInfo_canadaPost_SearchedAddress" class="personalInfoColorGray fontSize19 width100 uppercase">
            <div class="elementWithBoldFont">
                <span id="address_unit" name="address_unit"></span><span id="address_unit_hyphen">-</span>
                <span id="address_line1" name="address_line1"></span><span>, </span><br>
				<span id="address_line2" name="address_line2"></span><span  id="addressline2_br"><br></span>
				<span id="address_city" name="address_city"></span><span>, </span>			
				<span id="address_province" name="address_province"></span><br>
				<span id="address_postalcode" name="address_postalcode"></span>
            </div>
        </div>
		<br>
		<div id="personalData_canadaPostAddressDescription1" class="fontSize16 personalInfoColorGray elementWithTop17 paddingBottom40">
           <span data-translationKey="personalData_canadaPostAddressPara1"></span>
        </div>
		<center>
            <span id="personalData_EditAddressButton" class="personalInfo_CanadaPostButton greenColor">
                <span data-translationKey="personalData_EditAddressButton"></span>
			</span>
         </center>  
        <div id="personalData_enterAddressManuallySection" class="elementWithTop17 paddingBottom40">
        <div class="row stretchThisTable">
            <div class="column_one tdWidth15">
                <span data-translationKey="personalData_PreviousAddress_SuiteUnit" class="canadaPostInputBoxTitle personalInfoColorGray"></span><br>
                <input id="personalData_Address_SuiteUnit_TextField" placeholder="Unit #" autocomplete="off" autocorrect="off" spellcheck="false" type="text" class="canadaPostInput margin_title_text" name="multi-unit" maxlength="10" tabindex=13>
            </div>
            <div  class="column_one">
                <a class="tooltip" href="#" id="contactInfomation_infomation_button">
                    <span class="tooltiptext" data-translationKey="personalData_POBoxToolTipMsg"></span>
                </a>
             </div>
            <div class="marginCanadaPostLine1 column_one androidPaytdWidth">
                <span data-translationKey="personalData_PreviousAddress_AddressLine1" class="canadaPostInputBoxTitle personalInfoColorGray"></span><br>
                <input id="personalData_Address_AddressLine1_TextField" placeholder="Street Number, Street Name" autocomplete="off" autocorrect="off" spellcheck="false" type="text" class="canadaPostInput margin_title_text upperCaseField" name="AddressLine1" maxlength="40" tabindex=14>
            </div>
             
            <div id="personalData_AddressLine2Section" class="marginCanadaPostLine1 column_one androidPaytdWidth">
                <span data-translationKey="personalData_PreviousAddress_AddressLine2" class="canadaPostInputBoxTitle personalInfoColorGray"></span><br>
                <input id="personalData_Address_AddressLine2_TextField" placeholder="Mailing Address Line 2" autocomplete="off" autocorrect="off" spellcheck="false" type="text" class="canadaPostInput margin_title_text upperCaseField" name="AddressLine2" maxlength="40" tabindex=15 >
            </div>
        </div><br>
        <div class="row stretchThisTable">
            <div class="column_one tdWidth35">
                <span data-translationKey="personalData_PreviousAddress_City" class="canadaPostInputBoxTitle personalInfoColorGray"></span><br>
                <input id="personalData_Address_City_TextField" placeholder="City" autocomplete="off" autocorrect="off" spellcheck="false" type="text" class="canadaPostInput margin_title_text upperCaseField" name="city" maxlength="30" tabindex=16 >
            </div>
            <div class="marginCanadaPostLine2 inputTextAlignment">
                <span data-translationKey="personalData_PreviousAddress_Province" class="canadaPostInputBoxTitle personalInfoColorGray"></span><br>               
                <select id="personalData_Address_Province_TextField" placeholder="Province" type="text" class="canadaPostProvinceHeight margin_title_text upperCaseField" name="provinceCode" maxlength="30"></select>
            </div>
            <div class="column_one tdWidth20">
                <span data-translationKey="personalData_PreviousAddress_PostalCode" class="canadaPostInputBoxTitle personalInfoColorGray"></span><br>
                <input id="personalData_Address_PostalCode_TextField" placeholder="L#L#L#" autocomplete="off" autocorrect="off" spellcheck="false" type="text" class="canadaPostInput form-control margin_title_text upperCaseField" name="postcode" maxlength="6" tabindex=17>
            </div>
        </div>
        </div>
		<center>
            <span id="personalData_AcceptButton" class="personalInfo_CanadaPostButton greenColor">
                <span  data-translationKey="personalData_AcceptButton"></span>
			</span>
         </center>
        <p id="personalInfo_canadaPost_addressSearch_instructions" class="fontSize16 personalInfoColorGray">
            <span data-translationKey="personalData_canadaPostAddress_1"></span><br>
            <span data-translationKey="personalData_canadaPostAddress_2"></span><br><br>
            <span data-translationKey="personalData_canadaPostAddress_3"></span><br><br>
            <span data-translationKey="personalData_canadaPostAddress_4"></span>
         </p>
         <br>
         <center>
            <span id="personalData_EnterAddressManuallyButton" class="personalInfo_CanadaPostButton greenColor">
                <span  data-translationKey="personalData_EnterAdressManuallyButton"></span>
			</span>
         </center>
		 <br>
		 <br>
	<!--


	personalData_Address_ResidenceType					:	"ResidenceType:",
	personalData_Address_Own							:	"Own",
	personalData_Address_Rent							:	"Rent",
	personalData_Address_Parents						:	"With Parents",
	personalData_Address_Other							:	"Other",
		//-->

	<center>
		<table class="regularTableEx stretchThisTable tableNoCellsBorder" id="personalData_ResidenceTypeArea">
			<tr>
				<td class="heightForRadioButtonLabelCell labelPadingLeft15">
					<span class="fieldLabelsTitle" data-translationKey="personalData_Address_ResidenceType"></span>
				</td>
			</tr>
			<tr>
				<td>
					<center>
						<fieldset  id="personalData_Address_ResidenceType" data-role="controlgroup" data-type="horizontal" data-role="fieldcontain" class="actionButtonGroup ui-controlgroup ui-controlgroup-horizontal">
							<a id="personalData_Address_Own_RadioButton" href="#" data-theme="b" data-role="button" class="buttonItem ui-btn ui-corner-left ui-btn-up-c">
								<span id="residance_Own" class="ui-btn-inner" aria-hidden="true">
									<span class="ui-btn-text" data-translationKey="personalData_Address_Own"></span>
								</span>
							</a>
							<a id="personalData_Address_Rent_RadioButton" href="#" data-theme="b" data-role="button" class="buttonItem ui-btn ui-btn-up-c">
								<span id="residance_Rent" class="ui-btn-inner" aria-hidden="true">
									<span class="ui-btn-text" data-translationKey="personalData_Address_Rent"></span>
								</span>
							</a>
							<a id="personalData_Address_Parents_RadioButton" href="#" data-theme="b" data-role="button" class="buttonItem ui-btn ui-btn-up-c">
								<span id="residance_Parents" class="ui-btn-inner" aria-hidden="true">
									<span class="ui-btn-text" data-translationKey="personalData_Address_Parents"></span>
								</span>
							</a>
							<!-- US5131 WICI - Add Student Housing label to Residence Type list -->
							<a id="personalData_Address_Student_RadioButton" href="#" data-theme="b" data-role="button" class="buttonItem ui-btn ui-btn-up-c">
                            	<span id="residance_Student" class="ui-btn-inner" aria-hidden="true">
                                <span class="ui-btn-text" data-translationKey="personalData_Address_Student"></span>
                                </span>
                            </a>
							<a id="personalData_Address_Other_RadioButton" href="#" data-role="button" data-theme="b" data-inline="true" class="buttonItem ui-btn ui-corner-right ui-controlgroup-last ui-btn-up-c">
								<span id="residance_Other" class="ui-btn-inner" aria-hidden="true">
									<span class="ui-btn-text" data-translationKey="personalData_Address_Other"></span>
								</span>
							</a>
						</fieldset>
					</center>
				</td>
			</tr>
		</table>
	</center>
	<br>
	<br>
		<!--
	personalData_Address_MonthlyPayment				:	"Monthly House Payment (per month)",
	personalData_Address_Duration						:	"Duration at Current Address",
		//-->
		<center>
			<div id="personalData_HouseArea" >
	            <table id="addressLookupTable" class="stretchThisTable">
	                <tr class="fieldRow">
	                    <td class="fieldLabelsTopCell fieldCellSize33">
	                        <span data-translationKey="personalData_Address_MonthlyPayment"></span>
	                    </td>
	                    <td class="fieldValuesTopCell">
	                        <input class="fieldValuesTextField" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false" maxlength="8" id="personalData_Address_MonthlyPayment_TextField" tabindex=18 type='tel' max="999999" min="0" />
	                    </td>
	                </tr>

					<!-- Address duration block... -->

	                <tr class="fieldRow">
	                    <td class="fieldLabelsBottomCell fieldCellSize33">
	                        <span data-translationKey="personalData_Address_Duration"></span>
	                    </td>
	                    <td>

	                    	<div id="personalData_Duration">
		                    	<table class="durationControlTable">
								   	<tr>
								   		<td class="fieldValuesCell">
										<div>

											<div id="durationControlDiv">
												<table class="durationTableNoBorder">
													<tr>
														<td id="durationSpanSeparator" class="fieldValuesCell fieldCellSize5 durationSpanMinWidth">
								   							<span data-translationKey="personalData_Address_DurationYears"></span>&emsp;
								   						</td>

								   						<td id="durationCellsSeparator" class="fieldValuesCell fieldCellSize50">
									   						<div id="durationControlLeftPadding" class="numbers-row">
		        												<input type="tel" class="durationInput" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false" maxlength="3" id="personalData_Address_Duration_years_TextField" tabindex=19 min="0" max="100">
		      												</div>
	      												</td>
													</tr>
												</table>
											</div>

											<div id="durationControlDiv">
												<table class="durationTableNoBorder">
													<tr>
														<td class="fieldValuesCell fieldCellSize5 durationSpanMinWidth">
								   							<span data-translationKey="personalData_Address_DurationMonths"></span>&emsp;
								   						</td>

								   						<td id="durationCellsSeparatorPadding" class="fieldValuesCell fieldCellSize50">
									   						<div id="durationControlLeftPadding" class="numbers-row" >
		        												<input type="tel" class="durationInput" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false" maxlength="2" id="personalData_Address_Duration_months_TextField" tabindex=20 min="0" max="11">
		      												</div>
	      												</td>
													</tr>
												</table>
											</div>

										</div>
										</td>
								   	</tr>								   								   								   	
								</table>
							</div>

	                    </td>	                   	                 	                    
	                </tr>	                	                

					<!-- ...Address duration block -->
				
				<!-- US3623 Start --> 
	            <div>	            
	            <tr class="fieldRow" id="personalData_PreviousAddressYesNO">
	                <td class="fieldLabelsBottomCell fieldCellSize33">
	                    	<span data-translationKey="personalData_PreviousAddressWasInCanada"></span>
	                    </td>	                    
	                    <td class="fieldValuesTopCell">
	                        <div >
		                        <select name="flipPrevWasInCanada" id="flipPrevWasInCanada" data-role="slider">
									<option id="flipPrevWasInCanada_no" value="N">No</option>
									<option id="flipPrevWasInCanada_yes" value="Y" selected>Yes</option>
								</select>
							</div> 
	                    </td>
	            </tr>	            
	            </div>    
	            <!-- End -->
	            
	            </table>
			</div>
		</center>
		<br>
		<br>
		<div id='personalData_PreviousAddressArea' >
			<hr>
			<br>
			<br>

	<!--
		personalData_PreviousAddress_Title					:	"Previous Address",
		personalData_PreviousAddress_Description			:	"Only required if less than 2 years at current address",
	//-->	

	<!--
		personalData_PreviousAddress_AddressLine1			:	"Address Line 1",
		personalData_PreviousAddress_AddressLine2			:	"Address Line 2",
		personalData_PreviousAddress_SuiteUnit				:	"Suite/Unit",
		personalData_PreviousAddress_City					:	"City",
		personalData_PreviousAddress_Province				:	"Province"
	//-->

	<!-- US3623 Start -->
	<div id='personalData_PreviousAddressDetailsArea' >
	
            <div class="row">
              <div class="column elementWithBoldFont">
                <!-- VZE-265 -->
                   <span class="pageSubTitle fontSize30" data-translationKey="personalData_PreviousAddress_Title"></span> 
              </div>
              <div class="column_one">
                <span>
						<a class="tooltip personalInfomation_i_icon" id="personalInfomation_infomation_Pre_cityName">
             				<span class="tooltipCityNametext" data-translationKey="personalData_cityNameLongerThen24CharError_msg"></span>
                		</a>
                </span><br>
            </div>
          </div>
          <div class="row">
            <div class="column">
              <span class="pageSubTitle fontSize30" data-translationKey="personalData_PreviousAddress_Description"></span>
            </div>  
          </div>
		<br>
		
        <div class="row">
             <div class="column_one">
                 <center>
                 <div id="canadaPostLogo" class="personalInfo_CanadianPostLogo"></div>
                </center>
              </div>
              <div class="column_two">
                   <input id="streetPreviousAddress" placeholder="Start typing a street address or postal code" type="text" class="streetAddressCanadaPost upperCaseField" tabindex=21 >
              </div>
        </div>
        <br>
         <div id="personalInfo_canadaPost_SearchedPreviousAddress" class="personalInfoColorGray fontSize19 width100 uppercase">
            <div class="elementWithBoldFont">
                <span id="address_preunit" name="address_preunit"></span><span id="address_preunit_hyphen">-</span>
                <span id="address_previousLine1" name="address_previousLine1"></span><span>, </span><br>
				<span id="address_previousLine2" name="address_previousLine2"></span><span  id="address_previousLine2_br"><br></span>
				<span id="address_previousCity" name="address_previousCity"></span><span>, </span>			
				<span id="address_previousProvince" name="address_previousProvince"></span><br>
				<span id="address_previousPostalcode" name="address_previousPostalcode"></span>
            </div>
        </div>
		<br>
		<div id="personalData_canadaPostPreviousAddressDescription1" class="fontSize16 personalInfoColorGray elementWithTop17 paddingBottom40">
           <span data-translationKey="personalData_canadaPostAddressPara1"></span>
        </div>
		<center>
            <span id="personalData_EditAddressPreviousButton" class="personalInfo_CanadaPostButton greenColor">
                <span data-translationKey="personalData_EditAddressButton"></span>
			</span>
         </center>  
        <div id="personalData_enterAddressManuallyPreviousSection" class="elementWithTop17 paddingBottom40">
        <div class="row stretchThisTable">
            <div class="column_one tdWidth15">
                <span data-translationKey="personalData_PreviousAddress_SuiteUnit" class="canadaPostInputBoxTitle personalInfoColorGray"></span><br>
                <input id="personalData_PreviousAddress_SuiteUnit_TextField" placeholder="Unit #" autocomplete="off" autocorrect="off" spellcheck="false" type="text" class="canadaPostInput margin_title_text upperCaseField" name="previousMulti-unit" tabindex=22 >
            </div>
            <div  class="column_one">
                <a class="tooltip" href="#" id="personalInfo_pre_year_informationButton">
                    <span class="tooltiptext" data-translationKey="personalData_POBoxToolTipMsg"></span>
                </a>
             </div>
            <div class="marginCanadaPostLine1 column_one androidPaytdWidth">
                <span data-translationKey="personalData_PreviousAddress_AddressLine1" class="canadaPostInputBoxTitle personalInfoColorGray"></span><br>
                <input id="personalData_PreviousAddress_AddressLine1_TextField" placeholder="Street Number, Street Name" autocomplete="off" autocorrect="off" spellcheck="false" type="text" class="canadaPostInput margin_title_text upperCaseField" name="previousAddressLine1" maxlength="40" tabindex=23>
            </div>
            <div id="personalData_PreviousAddressLine2Section" class="marginCanadaPostLine1 column_one androidPaytdWidth">
                <span data-translationKey="personalData_PreviousAddress_AddressLine2" class="canadaPostInputBoxTitle personalInfoColorGray"></span><br>
                <input id="personalData_PreviousAddress_AddressLine2_TextField" placeholder="Mailing Address Line 2" autocomplete="off" autocorrect="off" spellcheck="false" type="text" class="canadaPostInput margin_title_text upperCaseField" name="previousAddressLine2" tabindex=24 >
            </div>
        </div><br>
        <div class="row stretchThisTable">
            <div class="column_one tdWidth35">
                <span data-translationKey="personalData_PreviousAddress_City" class="canadaPostInputBoxTitle personalInfoColorGray"></span><br>
                <input id="personalData_PreviousAddress_City_TextField" placeholder="City" autocomplete="off" autocorrect="off" spellcheck="false" type="text" class="canadaPostInput margin_title_text upperCaseField" name="previousCity" tabindex=25 >
            </div>
            <div class="marginCanadaPostLine2 inputTextAlignment">
                <span data-translationKey="personalData_PreviousAddress_Province" class="canadaPostInputBoxTitle personalInfoColorGray"></span><br>               
                <select id="personalData_PreviousAddress_Province_TextField" placeholder="Province" type="text" class="canadaPostProvinceHeight margin_title_text upperCaseField" name="previousProvinceCode" ></select>
            </div>
            <div class="column_one tdWidth20">
                <span data-translationKey="personalData_PreviousAddress_PostalCode" class="canadaPostInputBoxTitle personalInfoColorGray"></span><br>
                <input id="personalData_PreviousAddress_PostalCode_TextField" placeholder="L#L#L#" autocomplete="off" autocorrect="off" spellcheck="false" type="text" class="canadaPostInput form-control margin_title_text upperCaseField" name="previousPostalcode" tabindex=26>
            </div>
        </div>
        </div>
		<center>
            <span id="personalData_AcceptPreviousButton" class="personalInfo_CanadaPostButton greenColor">
                <span  data-translationKey="personalData_AcceptButton"></span>
			</span>
         </center>
        <p id="personalInfo_canadaPost_PreviousAddressSearch_instructions" class="fontSize16 personalInfoColorGray">
            <span data-translationKey="personalData_canadaPostAddress_1"></span><br>
            <span data-translationKey="personalData_canadaPostAddress_2"></span><br><br>
            <span data-translationKey="personalData_canadaPostAddress_3"></span><br><br>
            <span data-translationKey="personalData_canadaPostAddress_4"></span>
         </p>
         <br>
         <center>
            <span id="personalData_EnterAddressManuallyPreviousButton" class="personalInfo_CanadaPostButton greenColor">
                <span  data-translationKey="personalData_EnterAdressManuallyButton"></span>
			</span>
         </center>
		 <br>
	
			</div>
			<br>
			<br>
		</div>
		</center>
		<! -- US4709 -->
			
		<!--
	personalData_Correspondence							:	"Correspondence:",
	personalData_English								:	"English:",
	personalData_French									:	"French:",
		//-->

	<center>
		<table class="regularTableEx stretchThisTable tableNoCellsBorder" id="personalData_Correspondence_Group">
			<tr>
				<td class="heightForRadioButtonLabelCell labelPadingLeft15">
					<span class="fieldLabelsTitle" data-translationKey="personalData_Correspondence"></span>
				</td>
			</tr>

			<tr>
				<td>
					<center>
						<fieldset  id="personalData_Address_ResidenceType" data-role="controlgroup" data-type="horizontal" data-role="fieldcontain" class="actionButtonGroup ui-controlgroup ui-controlgroup-horizontal">
							<a id="personalData_English_RadioButton" href="#" data-theme="b" data-role="button" class="buttonItem ui-btn ui-corner-left ui-btn-up-c">
								<span class="ui-btn-inner" aria-hidden="true">
									<span class="ui-btn-text" data-translationKey="personalData_English"></span>
								</span>
							</a>

							<a id="personalData_French_RadioButton" href="#" data-role="button" data-theme="b" data-inline="true" class="buttonItem ui-btn ui-corner-right ui-controlgroup-last ui-btn-up-c">
								<span class="ui-btn-inner" aria-hidden="true">
									<span class="ui-btn-text" data-translationKey="personalData_French"></span>
								</span>
							</a>
						</fieldset >
					</center>
				</td>
			</tr>
		</table>
	</center>
	<br>
	<span class="phoneNote" data-translationKey="personalData_Note"></span>
	<br>
	<br>
	<!-- US3979 //-->	
		
</script>

<script id="WICIPersonalDataScreen2-template" type="text/x-jquery-tmpl">	
	
	<div id="BreadcrumbTrailArea1" class="breadcrumbHeader BreadcrumbTrailAreaLayoutStyle ScreenLayoutFullWidth">		
		<table id="BreadcrumbTable" class="stretchThisTable BreadcrumbTableStyle" cellspacing="0px" cellpadding="0px">

<!--
			<tr>
				<td class="selectedBreadcrumbCell">
					<span class="BreadcrumbTextNumberStyle selectedBreadcrumb selectedFirstBreadcrumbCell">1.</span>
					<span class="BreadcrumbTextStyle selectedBreadcrumb" data-translationKey="breadCrumbItem_ProductSelection"></span>
				</td>
				<td class="selectedBreadcrumbCell">
					<div class="visitedCrumbSeparator"/>
				</td>
				
				<td class="selectedBreadcrumbCell">
					<span class="BreadcrumbTextNumberStyle selectedBreadcrumb">2.</span>
					<span class="BreadcrumbTextStyle selectedBreadcrumb" data-translationKey="breadCrumbItem_PersonalData"></span>
				</td>
				<td class="unselectedBreadcrumbCell">
					<div class="visitedLeftCrumbSeparator"/>
				</td>
				
				<td class="unselectedBreadcrumbCell">
					<span class="BreadcrumbTextNumberStyle unselectedBreadcrumb">3.</span>
					<span class="BreadcrumbTextStyle unselectedBreadcrumb" data-translationKey="breadCrumbItem_OptionalProducts"></span>
				</td>
				
				<td class="unselectedBreadcrumbCell">
					<div class="notVisitedCrumbSeparator"/>
				</td>
				
				<td class="unselectedBreadcrumbCell">
					<span class="BreadcrumbTextNumberStyle unselectedBreadcrumb">4.</span>
					<span class="BreadcrumbTextStyle unselectedBreadcrumb" data-translationKey="breadCrumbItem_Confirmation"></span>
				</td>
			</tr>
-->


			<tr>
				<td class="selectedBreadcrumbCell">
					<div class="breadcrumbNumberDiv breadcrumbNumberDivFirst" >
						<span class="BreadcrumbTextNumberStyle selectedBreadcrumb selectedFirstBreadcrumbCell rightalign">1.</span>
					</div>
					<div class="breadcrumbTextDivFirst" >
						<span class="BreadcrumbTextStyle selectedBreadcrumb leftAlign" data-translationKey="breadCrumbItem_ProductSelection"></span>
					</div>
				</td>
				<td class="selectedBreadcrumbCell">
					<div class="visitedCrumbSeparator"/>
				</td>
				
				<td class="selectedBreadcrumbCell">
					<div class="breadcrumbNumberDiv">
						<span class="BreadcrumbTextNumberStyle selectedBreadcrumb rightalign">2.</span>
					</div>
					<div class="breadcrumbTextDiv">
						<span class="BreadcrumbTextStyle selectedBreadcrumb leftAlign" data-translationKey="breadCrumbItem_PersonalData"></span>
					</div>
				</td>
				<td class="unselectedBreadcrumbCell">
					<div class="visitedLeftCrumbSeparator"/>
				</td>
				
				<td class="unselectedBreadcrumbCell">
					<div class="breadcrumbNumberDiv">
						<span class="BreadcrumbTextNumberStyle unselectedBreadcrumb rightalign">3.</span>
					</div>
					<div class="breadcrumbTextDiv">
						<span class="BreadcrumbTextStyle unselectedBreadcrumb leftAlign" data-translationKey="breadCrumbItem_OptionalProducts"></span>
					</div
				</td>
				
				<td class="unselectedBreadcrumbCell">
					<div class="notVisitedCrumbSeparator"/>
				</td>
				
				<td class="unselectedBreadcrumbCell nowrap">
					<span class="BreadcrumbTextNumberStyle unselectedBreadcrumb">4.</span>
					<span class="BreadcrumbTextStyle unselectedBreadcrumb" data-translationKey="breadCrumbItem_Confirmation"></span>
				</td>
			</tr>


		</table>
	</div>
	
		
	<div class="ScreenLayout" id="PersonalDataScreen2_PageContents">
		<br>
		<br>		
		<center>
		<span id="P2ClickHere" class="pageTitle" data-translationKey="personalData2_AddressInformation"></span>
		</center>
		<br>
		<br>
		<!--
	personalData2_AddressInformation					:	"ADDRESS INFORMATION",
	personalData2_Address_PostalCode					:	"Postal Code",
	personalData2_Address_StreetNumber					:	"Street Number",
	personalData2_Address_Button_Label					:	"ADDRESS LOOKUP",
		
		//-->
		<center>
			<div id="personalData2_AddressLookupArea" >
	            <table id="addressLookupTable" class="stretchThisTable">
	                <tr class="fieldRow">
	                    <td class="fieldLabelsTopCell fieldCellSize33">
	                        <span data-translationKey="personalData2_Address_PostalCode"></span>
	                    </td>
	                    <td class="fieldValuesTopCell">
	                        <input class="fieldValuesTextField upperCaseField" id="personalData2_Address_PostalCode_TextField" tabindex=1 type="text" maxlength="6" />
	                    </td>
	                </tr>
	                <tr class="fieldRow">
	                    <td class="fieldLabelsBottomCell fieldCellSize33">
	                        <span data-translationKey="personalData2_Address_StreetNumber"></span>
	                    </td>
	                    <td class="fieldValuesBottomCell">
	                        <input class="fieldValuesTextField upperCaseField" id="personalData2_Address_StreetNumber_TextField" tabindex=2 type="text" maxlength="6" />
	                    </td>
	                </tr>
	            </table>
			</div>
		</center>
		<br>
        <span id="personalData2_AddressLookupButton" class="button greenflat addressLookupButtonWidth rightalign">
        	<span class="buttonText lookupSpan" data-translationKey="personalData2_Address_Button_Label"></span>
		</span>	
		<br>
		<br>
		<br>
		<br>
		<br>
		<br>	
		<br>	
		<!--
	personalData2_Address_AddressLine1					:	"Address Line 1",	
	personalData2_Address_AddressLine2					:	"Address Line 2",
	personalData2_Address_SuiteUnit						:	"Suite/Unit",
	personalData2_Address_City							:	"City*",
	personalData2_Address_Province						:	"Province*",
		//-->
		<center>
			<div id="personalData2_AddressArea" >
	            <table id="personalInfoTable" class="stretchThisTable">
	                <tr class="fieldRow">
	                    <td class="fieldLabelsTopCell fieldCellSize33">
	                        <span data-translationKey="personalData2_Address_AddressLine1"></span>
	                    </td>
	                    <td class="fieldValuesTopCell">
	                        <input class="fieldValuesTextField upperCaseField" id="personalData2_Address_AddressLine1_TextField" tabindex=3 type="text" maxlength="40" />
	                        <div id="addressLookup_Address_AddressLine1_MultipleControl"  class="addressLookup_DropDownItemsControl">
	                        	<span data-translationKey="addressLookup_multipleItemsExist"></span>
	                        	<select class="fieldValuesSelectField" id="personalData2_Address_AddressLine1_SelectField" >
								</select>
							</div>													
	                    </td>
	                </tr>
	 <!--          <tr class="fieldRow">
	                    <td class="fieldLabelsCell fieldCellSize33">
	                        <span data-translationKey="personalData2_Address_AddressLine2"></span>
	                    </td>
	                    <td class="fieldValuesCell">
	                        <input class="fieldValuesTextField upperCaseField" id="personalData2_Address_AddressLine2_TextField" tabindex=4 type="text" maxlength="40" />
							<div id="addressLookup_Address_AddressLine2_MultipleControl" class="addressLookup_DropDownItemsControl">								
								<span data-translationKey="addressLookup_multipleItemsExist"></span>
	                        	<select class="fieldValuesSelectField" id="personalData2_Address_AddressLine2_SelectField" >
								</select>
							</div>
	                    </td>
	                </tr>     -->
	                <tr class="fieldRow">
	                    <td class="fieldLabelsCell fieldCellSize33">
	                        <span data-translationKey="personalData2_Address_SuiteUnit"></span>
	                    </td>
	                    <td class="fieldValuesCell">
	                        <input class="fieldValuesTextField upperCaseField" id="personalData2_Address_SuiteUnit_TextField" tabindex=5 type="text" maxlength="5"/>
	                    </td>
	                </tr>
	                <tr class="fieldRow">
	                    <td class="fieldLabelsCell fieldCellSize33">
	                        <span data-translationKey="personalData2_Address_City"></span>
	                    </td>
	                    <td class="fieldValuesCell">
	                        <input class="fieldValuesTextField upperCaseField" id="personalData2_Address_City_TextField" tabindex=6 type="text" maxlength="24"/>
	                    </td>
	                </tr>
	                <tr class="fieldRow">
	                    <td class="fieldLabelsBottomCell fieldCellSize33">
	                        <span data-translationKey="personalData2_Address_Province"></span>
	                    </td>
	                    <td class="fieldValuesBottomCell">
	                        <!-- input class="fieldValuesTextField" id="personalData2_Address_Province_TextField" type="text"/ -->
							<select class="fieldValuesTextField" id="personalData2_Address_Province_TextField" tabindex=7>
							</select>
	                    </td>
	                </tr>
	            </table>
			</div>
		</center>
		<br>
		<br>
		<!--
		
	
	personalData2_Address_ResidenceType					:	"ResidenceType:",
	personalData2_Address_Own							:	"Own",
	personalData2_Address_Rent							:	"Rent",
	personalData2_Address_Parents						:	"With Parents",
	personalData2_Address_Other							:	"Other",
		//-->

	<center>
		<table class="regularTableEx stretchThisTable tableNoCellsBorder" id="personalData2_ResidenceTypeArea">
			<tr>
				<td class="heightForRadioButtonLabelCell labelPadingLeft15">
					<span class="fieldLabelsTitle" data-translationKey="personalData2_Address_ResidenceType"></span>					
				</td>
			</tr>			
			<tr class="sizeSelector">
				<td>
					<center>
						<fieldset  id="personalData2_Address_ResidenceType" data-role="controlgroup" data-type="horizontal" data-role="fieldcontain" class="actionButtonGroup ui-controlgroup ui-controlgroup-horizontal">
							<a id="personalData2_Address_Own_RadioButton" href="#" data-theme="b" data-role="button" class="buttonItem ui-btn ui-corner-left ui-btn-up-c">
								<span class="ui-btn-inner" aria-hidden="true">
									<span class="ui-btn-text" data-translationKey="personalData2_Address_Own"></span>
								</span>
							</a>
							<a id="personalData2_Address_Rent_RadioButton" href="#" data-theme="b" data-role="button" class="buttonItem ui-btn ui-btn-up-c">
								<span class="ui-btn-inner" aria-hidden="true">
									<span class="ui-btn-text" data-translationKey="personalData2_Address_Rent"></span>
								</span>
							</a>
							<a id="personalData2_Address_Parents_RadioButton" href="#" data-theme="b" data-role="button" class="buttonItem ui-btn ui-btn-up-c">
								<span class="ui-btn-inner" aria-hidden="true">
									<span class="ui-btn-text" data-translationKey="personalData2_Address_Parents"></span>
								</span>
							</a>
							<a id="personalData2_Address_Other_RadioButton" href="#" data-role="button" data-theme="b" data-inline="true" class="buttonItem ui-btn ui-corner-right ui-controlgroup-last ui-btn-up-c">
								<span class="ui-btn-inner" aria-hidden="true">
									<span class="ui-btn-text" data-translationKey="personalData2_Address_Other"></span>
								</span>
							</a>		
						</fieldset>
					</center>			
				</td>
			</tr>
		</table>
	</center>
	<br>
	<br>
		<!--
	personalData2_Address_MonthlyPayment				:	"Monthly House Payment (per month)",
	personalData2_Address_Duration						:	"Duration at Current Address",			
		//-->
		<center>
			<div id="personalData2_HouseArea" >
	            <table id="addressLookupTable" class="stretchThisTable">
	                <tr class="fieldRow">
	                    <td class="fieldLabelsTopCell fieldCellSize33">
	                        <span data-translationKey="personalData2_Address_MonthlyPayment"></span>
	                    </td>
	                    <td class="fieldValuesTopCell">
	                        <input class="fieldValuesTextField" id="personalData2_Address_MonthlyPayment_TextField" tabindex=8 type='tel' max="9999" min="0" />
	                    </td>
	                </tr>

					<!-- Address duration block... -->

	                <tr class="fieldRow">
	                    <td class="fieldLabelsBottomCell fieldCellSize33">
	                        <span data-translationKey="personalData2_Address_Duration"></span>
	                    </td>
	                    <td>

	                    	<div id="personalData2_Duration">
		                    	<table class="durationControlTable">
								   	<tr>
								   		<td class="fieldValuesCell>
										<div>								
		
											<div id="durationControlDiv">
												<table class="durationTableNoBorder">
													<tr>
														<td id="durationSpanSeparator" class="fieldValuesCell fieldCellSize5 durationSpanMinWidth">
								   							<span data-translationKey="personalData2_Address_DurationYears"></span>&emsp;
								   						</td>
								   		
								   						<td id="durationCellsSeparator" class="fieldValuesCell fieldCellSize50">
									   						<div id="durationControlLeftPadding" class="numbers-row">
		        												<input type="tel" class="durationInput" id="personalData2_Address_Duration_years_TextField" tabindex=9 min="0" max="100">
		      												</div>
	      												</td>
													</tr>	
												</table>
											</div>

											<div id="durationControlDiv">
												<table class="durationTableNoBorder">
													<tr>
														<td class="fieldValuesCell fieldCellSize5 durationSpanMinWidth">
								   							<span data-translationKey="personalData2_Address_DurationMonths"></span>&emsp;
								   						</td>

								   						<td id="durationCellsSeparatorPadding" class="fieldValuesCell fieldCellSize50">
									   						<div id="durationControlLeftPadding" class="numbers-row" >
		        												<input type="tel" class="durationInput" id="personalData2_Address_Duration_months_TextField" tabindex=10 min="0" max="11">
		      												</div>
	      												</td>
													</tr>	
												</table>
											</div>		
									
										</div>
										</td>
								   	</tr>								   	
								</table>
							</div>

	                    </td>
	                </tr>

					<!-- ...Address duration block -->

	            </table>
			</div>
		</center>
		<br>
		<br>
		<div id='personalData2_PreviousAddressArea' > 		
		<hr>
		<br>
		<br>

<!--		
	personalData2_PreviousAddress_Title					:	"Previous Address",
	personalData2_PreviousAddress_Description			:	"Only required if less than 2 years at current address",
//-->

		<center>
			<span class="pageSubTitle" data-translationKey="personalData2_PreviousAddress_Title"></span>
			<br>
			<span class="pageSubTitle" data-translationKey="personalData2_PreviousAddress_Description"></span>
		</center>
		<br>
<!--	
	personalData2_PreviousAddress_PostalCode			:	"Postal Code",	
	personalData2_PreviousAddress_StreetNumber			:	"Street Number",	
	personalData2_PreviousAddress_Button_Label			:	"ADDRESS LOOKUP",
//-->

		<center>
			<div id="personalData2_PreviousAddressLookupArea" >
	            <table id="addressLookupTable" class="stretchThisTable">
	                <tr class="fieldRow">
	                    <td class="fieldLabelsTopCell fieldCellSize33">
	                        <span data-translationKey="personalData2_PreviousAddress_PostalCode"></span>
	                    </td>
	                    <td class="fieldValuesTopCell">
	                        <input class="fieldValuesTextField upperCaseField" id="personalData2_PreviousAddress_PostalCode_TextField" tabindex=11 type="text" maxlength="6"/>
	                    </td>
	                </tr>
	                <tr class="fieldRow">
	                    <td class="fieldLabelsBottomCell fieldCellSize33">
	                        <span data-translationKey="personalData2_PreviousAddress_StreetNumber"></span>
	                    </td>
	                    <td class="fieldValuesBottomCell">
	                        <input class="fieldValuesTextField upperCaseField" id="personalData2_PreviousAddress_StreetNumber_TextField" tabindex=12 type="text" maxlength="6"/>
	                    </td>
	                </tr>
	            </table>
			</div>
		</center>
		<br>
        <span id="personalData2_PreviousAddressLookupButton" class="button greenflat addressLookupButtonWidth rightalign">
        	<span class="buttonText lookupSpan" data-translationKey="personalData2_PreviousAddress_Button_Label"></span>
		</span>
		<br>
		<br>
		<br>
		<br>
		<br>
		<br>
		<br>
<!--		
	personalData2_PreviousAddress_AddressLine1			:	"Address Line 1",	
	personalData2_PreviousAddress_AddressLine2			:	"Address Line 2",
	personalData2_PreviousAddress_SuiteUnit				:	"Suite/Unit",
	personalData2_PreviousAddress_City					:	"City*",
	personalData2_PreviousAddress_Province				:	"Province*"	
//-->		
		<center>
			<div id="personalData2_AddressArea" >
	            <table id="personalInfoTable" class="stretchThisTable">
	                <tr class="fieldRow">
	                    <td class="fieldLabelsTopCell fieldCellSize33">
	                        <span data-translationKey="personalData2_PreviousAddress_AddressLine1"></span>
	                    </td>
	                    <td class="fieldValuesTopCell">
	                        <input class="fieldValuesTextField upperCaseField" id="personalData2_PreviousAddress_AddressLine1_TextField" type="text" maxlength="40" tabindex=13/>
	                        <div id="addressLookup_PreviousAddress_AddressLine1_MultipleControl"  class="addressLookup_DropDownItemsControl">
	                        	<span data-translationKey="addressLookup_multipleItemsExist"></span>
		                        <select class="fieldValuesSelectField" id="personalData2_PreviousAddress_AddressLine1_SelectField" >
								</select>							
							</div>								
	                    </td>
	                </tr>
<!--	            <tr class="fieldRow">
	                    <td class="fieldLabelsCell fieldCellSize33">
	                        <span data-translationKey="personalData2_PreviousAddress_AddressLine2"></span>
	                    </td>
	                    <td class="fieldValuesCell">
	                        <input class="fieldValuesTextField upperCaseField" id="personalData2_PreviousAddress_AddressLine2_TextField" type="text" maxlength="40" tabindex=13/>
	                        <div id="addressLookup_PreviousAddress_AddressLine2_MultipleControl"  class="addressLookup_DropDownItemsControl">
	                        	<span data-translationKey="addressLookup_multipleItemsExist"></span>
		                        <select class="fieldValuesSelectField" id="personalData2_PreviousAddress_AddressLine2_SelectField" >
								</select>
							</div>															
	                    </td>
	                </tr>    -->
	                <tr class="fieldRow">
	                    <td class="fieldLabelsCell fieldCellSize33">
	                        <span data-translationKey="personalData2_PreviousAddress_SuiteUnit"></span>
	                    </td>
	                    <td class="fieldValuesCell">
	                        <input class="fieldValuesTextField upperCaseField" id="personalData2_PreviousAddress_SuiteUnit_TextField" type="text" maxlength="5" tabindex=14/>
	                    </td>
	                </tr>
	                <tr class="fieldRow">
	                    <td class="fieldLabelsCell fieldCellSize33">
	                        <span data-translationKey="personalData2_PreviousAddress_City"></span>
	                    </td>
	                    <td class="fieldValuesCell">
	                        <input class="fieldValuesTextField upperCaseField" id="personalData2_PreviousAddress_City_TextField" type="text" maxlength="24" tabindex=15/>
	                    </td>
	                </tr>
	                <tr class="fieldRow">
	                    <td class="fieldLabelsBottomCell fieldCellSize33">
	                        <span data-translationKey="personalData2_PreviousAddress_Province"></span>
	                    </td>
	                    <td class="fieldValuesBottomCell">
	                        <!-- input class="fieldValuesTextField" id="personalData2_PreviousAddress_Province_TextField" type="text"/ -->
							<select class="fieldValuesTextField" id="personalData2_PreviousAddress_Province_TextField" tabindex=16 >
							</select>
	                    </td>
	                </tr>
	            </table>
			</div>
		</center>
		<br>
		<br>
</div>		
	</div>
</script>
<script id="WICIPrintDemoScreen-template" type="text/x-jquery-tmpl">
    <div class="ScreenLayout marginLeft25" id="printScreen_PageContents">
	<br>
	<br>
	<br>
	{{if appStatusFromResponse === 'DECLINED' }}
	
		<center>
			<div data-role="fieldcontain" class="printTest">
	        	<span id="cardSelection_DecliancePage_title" class="pageTitle" data-translationKey="printScreen_Title"></span>
	         	<br><br>
					<span id="printScreen_subTitle_Id" class="summaryPageSubTitle" data-translationKey="printScreen_SubTitle_OMP_OMR"></span>
					
					{{if dupConvenceEnable === 'Y' }}
					
					<span class="summaryPageSubTitle" data-translationKey="signature_OptionsMasterCard_Dupconvence_text">dupConvenceEnable</span>
					
					{{/if}}
					
						
					<span id="cardSelection_DecliancePage_Id" class="summaryPageSubTitle" data-translationKey="${keyForDisplayingCardSelection}"></span>
	         	<br><br>
					<span id ="keyForApplicationStatusId" class="summaryPageSubTitle" data-translationKey="${keyForDisplayingApplicationStatus}" />
					
					{{if dupConvenceEnable === 'Y' }}
					
					<span data-translationKey="${keyForDisplayingApplicationStatus}" />
					
					{{/if}}
				<br>
	        </div>
			<br><br><br>
		</center>
		
	{{else (appStatusFromResponse === 'APPROVED' && (consentGranted === 'N')) || ((consentGranted === 'G' || consentGranted === 'A') && queueName !== 'DIIAPPRV') }}
		{{if respCardType === 'OMX' || respCardType === 'OMZ'}}	
		 <br>	
			<center>
		         	{{if  respCardType  === 'OMX' }}
		         	     <a href="#" id="omxCardApprovalNoThanks" class="omxCardApprovalNoThanks"></a>
						
					{{else respCardType === 'OMZ' }}
						 <a href="#" id="omzCardApprovalNoThanks" class="omzCardApprovalNoThanks"></a>
					{{/if}}					
			</center>
		{{else}}
		 	<center>
				<div data-role="fieldcontain" class="printTest">
		        	<span class="pageTitle" data-translationKey="printScreen_Title"></span>
		         	<br><br>
						<span class="summaryPageSubTitle" data-translationKey="printScreen_SubTitle_OMP_OMR"></span>	
						<span class="summaryPageSubTitle" data-translationKey="${keyForDisplayingCardSelection}"></span>
		         	<br><br>
						<span class="summaryPageSubTitle" data-translationKey="${keyForDisplayingApplicationStatus}" />
					<br>
		        </div>
				<br><br>
			</center>
		{{/if}}
		
	{{else appStatusFromResponse === 'APPROVED' && ((consentGranted === 'G' || consentGranted === 'A') && queueName === 'DIIAPPRV')}}
		
			<div data-role="fieldcontain" >
	        	<span class="printPageTitle" data-translationKey="printScreen_Title"></span>
	         	<br><br><br>
	         	{{if respCardType === 'OMX' || respCardType === 'OMZ'}}
	         		<span class="printPageSubTitle" data-translationKey="${keyForDisplayingApplicationStatus}" />
	         		<span class="printPageSubTitle" data-translationKey="${keyForDisplayingCardSelection}" />
					<span class="printPageSubTitle" data-translationKey="printScreen_ApplicationApproved_OMX_OMZ_Approved"></span>
				{{else}}
					<span class="printPageSubTitle" data-translationKey="printScreen_SubTitle_OMP_OMR"></span>	
					<span class="printPageSubTitle" data-translationKey="${keyForDisplayingCardSelection}"></span>
					<span class="printPageSubTitle" data-translationKey="printScreen_PayParaSymbol"></span>
		         	<br><br><br>
					<span class="printPageSubTitle" data-translationKey="${keyForDisplayingApplicationStatus}" />
				{{/if}}
				<br><br><br>
				<center>
				{{if  respCardType  === 'OMX' }}
						<img id="SignatureImage" src="app/images/omx_en.png" data-lang-src width="320""/>
			    {{else respCardType === 'OMZ' }}
						<img id="SignatureImage" src="app/images/omz_en.png" data-lang-src width="320""/>
			     {{else respCardType === 'OMP' }}
						<img id="SignatureImage" src="app/images/omp_en.png" data-lang-src width="320""/>
				{{else}}
						<img id="SignatureImage" src="app/images/omr_en.png" data-lang-src width="320""/>					
			    {{/if}}	
				</center>
				<br><br>
				<span class="printPageSubTitle" data-translationKey="printScreen_ReceiveCard" />
				<br><br><br>
				{{if consentGranted === 'G' }}
				     <span class="printPageSubTitle" data-translationKey="printScreen_Register_googlePay" />
				     <span class="printPageSubTitle" data-translationKey="printScreen_Register" />
				{{else consentGranted === 'A' }}
				     <span class="printPageSubTitle" data-translationKey="printScreen_Register_applePay" />
				     <span class="printPageSubTitle" data-translationKey="printScreen_Register" />
				{{/if}}
				<br><br>
				<!-- <table class="printAndroidApplePayTable">
						<tr>
							<td class="androidApplePaytd">
								<div class="agentRadiobutton">
		                    		<input type="radio" id="printScreenAndroidPay_CheckField" name="payRadio" value="android" checked>
		                    	</div>						
								<a href="#" id="androidPayInsIss" class="androidApplePay bgCenter" data-lang-class>								
	                        		<span class="pay_Labels" data-translationKey="printScreen_MobilePayments_Para4"></span>
	                    		</a>
							</td>
							<td class="androidApplePaytd">
								<div class="agentRadiobutton">
		                    		<input type="radio" id="printScreenApplePay_CheckField" name="payRadio" value="apple">
		                    	</div>
								<a href="#" id="applePay" class="androidApplePay bgCenter" data-lang-class>								
	                        		<span class="pay_Labels" data-translationKey="personalData_MobilePayments_Para5"></span>
	                    		</a>
							</td>
						</tr>
				</table> -->
				<br><br>
				<table class="printAndroidApplePayTable">
				   <tr>
				       <td class="tdWidth10">
							<span id="printScreen_BeginSetupButton" class="button greenflat afterPrintButtonWidth uppercase">
					           	<span class="buttonText" data-translationKey="print_BeginSetup_Button"></span>
							</span>
					   </td>
					   <td class="tdWidth20 borderLeftTDWhite">
					   
					        {{if  consentGranted === 'G' }}
						        <a href="#" id="androidPayInsIss" class="androidApplePay bgCenter" data-lang-class>								
		                        		<span class="pay_Labels" data-translationKey="printScreen_MobilePayments_Para4"></span>
		                        </a>
	                         {{else consentGranted === 'A' }}
		                        <a href="#" id="applePayConfiramtionPage" class="androidApplePay bgCenter" data-lang-class>								
		                        		<span class="pay_Labels" data-translationKey="personalData_MobilePayments_Para5"></span>
		                        </a>
	                        {{/if}}	
					   </td>
				   </tr>	
				</table>
	        </div>
		
		{{/if}}
		<br>
        <br>
        <br>
	    <center>
            <span id="print_StartNewApplicationButton" class="button greenflat afterPrintButtonWidth uppercase">
            	<span class="buttonText" data-translationKey="printResponseStatus_StartNewApplication_Button"></span>
			</span><br>
			<span id="print_LogOutButton" class="button darkgrayflat afterPrintButtonWidth uppercase">
            	<span class="buttonText" data-translationKey="printResponseStatus_LogOut_Button"></span>
			</span>
        </center>
    </div>
</script>

<script id="WICISignatureScreen-template" type="text/x-jquery-tmpl"> 
	<div class="ScreenLayout" id="SignatureScreen_PageContents">
		<br>
		<div class="overlay" id="signatureHandoutTabToRepDialog-container">
  			<div class="handoutTabToCustomerpopup_chooseProduct">
    			<p class="text-left" data-translationKey="mobilePayment_HandoutTabToRepDialogContent"></p>
    			<div>      				
      				<button class="dialog-btn-ok" id="signatureHandoutToRepOk" data-translationKey="mobilePayment_HandoutTabToRepDialogOk"></button>
					<button class="dialog-btn-cancel" id="signatureHandoutToRepCancel" data-translationKey="mobilePayment_HandoutTabToRepDialogCancel"></button>
    			</div>
  			</div>
		</div>
		<div id="SignatureScreen_OMZContents"
		</br>
	  				<div>
					 <center>  <img id="omzChartImage" src="app/images/OMZ_Qualifier.png" data-lang-class/> </center>
	 				</div>
		 </br>
		
		   <div id="chooseProductScreen_PageHeader">
		      <center>
              <span class="sigScreen_ChooseCardTitle" data-translationKey="sigScreen_ChooseCardRightForYou"></span>
               <br>
               <span id="sigScreenOMZ_textId" data-translationKey="sigScreen_OMZCard_Note"></span>
              </center>
                <br>               
                 <ul id="signatureScreenProductList">
                 <table align="center" style="border: none;>
		         <tr>
				 <li>
				 <td id="signatureScreen_WorldEliteCardContent" >
                   <span class="sigScreen_WorldEliteCardTitle" id="sigScreen_WorldEliteCardTitle_1"  data-translationKey="sigScreen_WorldEliteCard_text"></span>
                   <a href="#" id="signatureScreenOmzCard"  class="creditCardProductClickableProduct"></a><br> 
                   <center><span class="sigScreen_WorldEliteCardNote_1" data-translationKey="sigScreen_WorldEliteCard_Note_text"></span>
                   <span class="sigScreen_WorldEliteCardNote_2" data-translationKey="sigScreen_WorldEliteCard_Note_text1"></span>
                   <span class="sigScreen_WorldEliteCardNote_3" data-translationKey="sigScreen_WorldEliteCard_Note_text2"></span>
                   <span class="sigScreen_WorldEliteCardNote_4" data-translationKey="sigScreen_WorldEliteCard_Note_text3"></span>
                   <span class="sigScreen_WorldEliteCardNote_5" data-translationKey="sigScreen_WorldEliteCard_Note_text4"></span>
                  </center>
                  </td>
                </li>
                 <td id="signatureScreen_text"> </td>
                <li>
                 <td id="signatureScreen_TriangleCardContent">
                  
                   <span  class="sigScreen_TriangleCardTitle " data-translationKey="sigScreen_TriangleCard_text"> </span>
                    <a href="#" id="signatureScreenOmcCard" class="creditCardProductClickableProduct ">
                    </a><br>
                  <center>  <span class="sigScreen_WorldEliteCardNote" style="visibility: hidden;" data-translationKey="sigScreen_WorldEliteCard_Note_text"></span>
                    <span class="sigScreen_WorldEliteCardNote" style="visibility: hidden;" data-translationKey="sigScreen_WorldEliteCard_Note_text1"></span>
                    <span class="sigScreen_WorldEliteCardNote"  style="visibility: hidden;" data-translationKey="sigScreen_WorldEliteCard_Note_text2"></span>
                    <span class="sigScreen_WorldEliteCardNote"  style="visibility: hidden;" data-translationKey="sigScreen_WorldEliteCard_Note_text3"></span>
                    <span class="sigScreen_WorldEliteCardNote"  style="visibility: hidden;" data-translationKey="sigScreen_WorldEliteCard_Note_text4"></span>
                    </center>
                   </td>
                   
                </li>
                </tr> 
                </table>
               </ul>
           
         </div>
            <br />
		 <div class="signature_Omc">
		 <table class="overviewCenterTableContainer cocd_table">
           <tr>
          <td style="border: none;">
         <table class="overviewCenterTable">
                    <tr>
                    <th colspan="2" id="overviewCenterTableTitlePadding" data-translationKey="overview_CostOfCreditDisclosure_Title"></th>
                   </tr>
                    <tr>
                    <td class="overviewCenterTableTitle" data-translationKey="overview_CostOfCreditDisclosure_Left1"></td>
                    <td class="overviewCenterTableData">
                        <p id="overviewCenterTableTextWithoutMargin" class="omx_overview_CostOfCreditDisclosure" data-translationKey="overview_CostOfCreditDisclosure_Right1"></p>
                        <p id="overviewCenterTableTextWithoutMargin" class="omz_overview_CostOfCreditDisclosure" data-translationKey="overview_CostOfCreditDisclosure_OMZ_Right1"></p>
                    </td>
                    </tr>
                    <tr>
                    <td class="overviewCenterTableTitle" data-translationKey="overview_CostOfCreditDisclosure_Left2"></td>
                    <td class="overviewCenterTableData">
                        <p id="overviewCenterTableTextWithoutMargin" data-translationKey="overview_CostOfCreditDisclosure_Right2"></p>
                    </td>
                    </tr>
                    <tr>
                    <td class="overviewCenterTableTitle" data-translationKey="overview_CostOfCreditDisclosure_Left3"></td>
                    <td class="overviewCenterTableData">
                        <p id="overviewCenterTableTextWithoutMargin" data-translationKey="overview_CostOfCreditDisclosure_Right3"></p>
                    </td>
                    </tr>
                    <tr>
                    <td class="overviewCenterTableTitle" data-translationKey="overview_CostOfCreditDisclosure_Left4"></td>
                    <td class="overviewCenterTableData">
                        <p id="overviewCenterTableTextWithoutMargin" data-translationKey="overview_CostOfCreditDisclosure_Right4"></p>
                    </td>
                    </tr>
                    <tr>
                    <td class="overviewCenterTableTitle" data-translationKey="overview_CostOfCreditDisclosure_Left5"></td>
                    <td class="overviewCenterTableData">
                        <p id="overviewCenterTableTextWithoutMargin" data-translationKey="overview_CostOfCreditDisclosure_Right5"></p>
                    </td>
                    </tr>
                    <tr>
                    <td class="overviewCenterTableTitle" style="border-bottom: none !important;" data-translationKey="overview_CostOfCreditDisclosure_Left6"></td>
                    <td class="overviewCenterTableData" style="border-bottom: none !important;">
                        <p id="overviewCenterTableTextWithoutMargin" data-translationKey="overview_CostOfCreditDisclosure_Right6"></p>
                    </td>
                    </tr>
            </table>
             </td>
        </td>
    </tr>
</table>
</div>

<div class="overViewInterest">
<span data-translationKey="overview_AccrualInterest"></span>
<br><br>
</div>

<div class="overViewInterest">
<span data-translationKey="overview_InterestRates"></span>
</div>
<div class="overviewCenterTableContainer elementWithLeft10 bottomSpacing elementWithFontSize10" data-translationKey="overview_EffectiveDate"></div>
<div id="triangle_world_ELite_MasterCardNote" class="overviewCenterTableContainer elementWithLeft15  elementWithFontSize10" data-translationKey="overview_Triangle_world_ELite_MasterCardNote"></div>
</div>
<br>
		
		
		
		<br>
		<center>
			<span id="SigClickHere" class="pageTitle" data-translationKey="sigScreen_Title"></span>
		</center>
		<br>
		<p><span data-translationKey="signatureScreen_Header"/></p>

		<ul>
			<li>
				<span data-translationKey="signatureScreen_License1" />
				<!-- <span id="signatureCardName" /> //-->
				{{if CardType === 'OMR' }}
					<span data-translationKey="signature_CashAdvantageMasterCard" ></span>
				{{else CardType === 'OMP' }}
					<span data-translationKey="signature_GasAdvantageMasterCard1_withoutSuperScript" ></span>
				{{else}}
					<span id="triangle_MasterCard_License1" data-translationKey="signature_OptionsMasterCard_withoutSuperScript" ></span>
					<span id="worldElite_MasterCard_License1" data-translationKey="signature_World_ELiteMasterCard_withoutSuperScript" ></span>	
				{{/if}}
				<span data-translationKey="signatureScreen_License1_1" />
				<span data-translationKey="signatureScreen_License1_2" />
			</li>
			<br>
			<li id ="omx_signatureScreen_Licence_Content" class="paddingBottom15" > <span  data-translationKey="signatureScreen_License2"/></li>
			<!--<li id ="omz_signatureScreen_Licence_Content"> <span  data-translationKey="signatureScreen_License2_OMZ"/></li>-->

				{{if CardType === 'OMR' }}
					<li><span data-translationKey="signatureScreen_License3_OMR"/></li>
				{{else CardType === 'OMP' }}
					<li><span data-translationKey="signatureScreen_License3_OMP"/></li>
				{{else}}
					<li><span data-translationKey="signatureScreen_License3"/></li>
				{{/if}}

			<br>
			<li><span data-translationKey="signatureScreen_License4"/></li>
			<br>
			<li><span data-translationKey="signatureScreen_License5"/></li>
			<br>
			<li>
				<b>
					<span data-translationKey="signatureScreen_License6"/>
				</b>
			</li>
			<br>
			<li>
				<b>
					<span data-translationKey="signatureScreen_License6_newLine"/>
				</b>
			</li>
			<br>
			<li><span data-translationKey="signatureScreen_License7"/></li>
			<br>		
			{{if CardType === 'OMP' }}
	{{else}} 	
		<li><span data-translationKey="signatureScreen_License7a"/></li>
		<br>
		<li id = "signature_Text_OMX"><span data-translationKey="signatureScreen_License7b_OMX"/></li>			
		<li id = "signature_Text_OMZ"><span data-translationKey="signatureScreen_License7b"/></li>
		<br>
{{/if}}

			<li><span data-translationKey="signatureScreen_License8"/></li>
			<br>
			<li><span data-translationKey="signatureScreen_License9"/></li>
			<br>
			<li><span data-translationKey="signatureScreen_License10"/></li>
			<!-- QC province --->
            {{if CardType === 'OMP' }}
            <li id="newBulletForQC_1" class="hidden"><span data-translationKey="signatureScreen_License11_QC_OMP_1"/></li>
			<li id="newBulletForQC_2" class="hidden"><span data-translationKey="signatureScreen_License11_QC_OMP_2"/></li>
            {{else CardType === 'OMX' || CardType === 'OMZ' }}
            <li id="newBulletForQC_OMX_1" class="hidden"><span data-translationKey="signatureScreen_License11_QC_OMX_1"/></li>
			<li id="newBulletForQC_OMX_2" class="hidden"><span data-translationKey="signatureScreen_License11_QC_OMX_2"/></li>
 			{{/if}} 
           <!-- QC province --->
			<!--<br>//-->
			<!--<br>//-->
			<!--<li><span data-translationKey="signatureScreen_License4"/></li>//-->
			<!--<br>//-->
			<!--<br>//-->
			<!--<li><span data-translationKey="signatureScreen_License5"/></li>//-->
		</ul>
		<br>
		<div class="warningDIV" id="warningDIVSig" name="warningDIVSig">
		<center>
		<p class="warningHeader_signaturePage" id="warningHeaderSig" name="warningHeaderSig"><span data-translationKey="signatureScreen_WarningHeader"/></p>
        <center>
	    <table class="warningTable" id="warningTableSig" name="warningTableSig" >
        <tr>
            <td>
							{{if CardType === 'OMR' }}
							<!-- US3766 //-->
								<img id="SignatureImage" src="app/images/omr_en.png" data-lang-src width="180" style="margin-top:10px;"/>
							{{else CardType === 'OMP' }}
								<img id="SignatureImage" src="app/images/omp_en.png" data-lang-src width="180" style="margin-top:10px;"/>
							{{else CardType === 'OMZ' }}
								<img id="SignatureImage" src="app/images/omz_en.png" data-lang-src width="180" style="margin-top:10px;"/>	
							{{else}}
								<img id="SignatureImage" src="app/images/omx_en.png" data-lang-src width="180" style="margin-top:10px;"/>
							{{/if}}
							<br/>
           </td>
           <!-- US5147 WICI - Updated Signature Box -->
            <td style="width: 80%; padding: 13px 5px 16px 25px;">
                <span class="signaturePageFont" id="warningText_SignaturePage" data-translationKey="signatureScreen_WarningText1" ></span><br/><br/><b>
					{{if CardType === 'OMR' }}
						<span class="signaturePageCardFont" id="CashAdvantageMasterCard_CreditCard" data-translationKey="signature_CashAdvantageMasterCard_CreditCard" ></span>
					{{else CardType === 'OMP' }}
						<span class="signaturePageCardFont" id="GasAdvantageMasterCard_CreditCard" data-translationKey="signature_GasAdvantageMasterCard_CreditCard" ></span>
					{{else}}
						<span class="signaturePageCardFont" id="triangelMasterCard" data-translationKey="signature_OptionsMasterCard_CreditCard" ></span>
					    <span class="signaturePageCardFont" id="worldElite_MasterCard" data-translationKey="signature_World_ELiteMasterCard_CreditCard" ></span>		
					{{/if}}
				</b>
            </td>
        </tr>
        </table>
        </center>
        </center>

			<!-- <div class="signatureContainer sugnatureFixes" >
				<div class="signatureBorder" id="signatureScreen_SignatureContainer">
					<div class="signature" id="signature"/>
				</div>
			</div> -->
		</center>
			<div class="signatureCredentialsContainer signatureTopPadding">
				<label id="signatureScreen_UserName">${ClientName}</label>

				<p class="margin_Signature"><span data-translationKey="sigScreen_Date"/><label id="signatureScreen_SignDate"></p>

				<div id="signatureScreen_AgreementBoxContainer" class="agreementBoxContainer align-vertical-middle-container">
					<div class="rightContaier align-vertical-middle-item">
							<div class="wiciCheckBox" >
								<input type="checkbox" name="signatureScreen_AcceptAgreement" id="signatureScreen_AcceptAgreement" />
								<label for="signatureScreen_AcceptAgreement"/>
							</div>
					</div>
					<div class="leftContainer opCheckBoxLabel align-vertical-middle-item">
					<!-- US3766 //-->
						<label data-translationKey="signatureScreen_TermsAndConditions_AcceptBox" />
							{{if CardType === 'OMR' }}
								<span data-translationKey="signature_CashAdvantageMasterCard" ></span>
								 <span data-translationKey="signatureScreen_TermsAndConditions_AcceptBox_MSPVerification"> </span>
							{{else CardType === 'OMP' }}
								<span data-translationKey="signature_GasAdvantageMasterCard" ></span>
								<span data-translationKey="signatureScreen_TermsAndConditions_AcceptBox_MSPVerification"> </span>
							{{else}}
								<span id="sigtriangelMasterCardNote" data-translationKey="signature_OptionsMasterCard"></span>
							    <span id="sigworldElite_MasterCardNote" data-translationKey="signature_World_ELiteMasterCard"> </span>
							    <span data-translationKey="signatureScreen_TermsAndConditions_AcceptBox_MSPVerification"> </span>
							{{/if}}
					</div>
				</div>
				<br>
				<div id="signatureScreen_AgreementBoxContainerNew" class="agreementBoxContainer align-vertical-middle-container">
					<div class="rightContaier align-vertical-middle-item">
							<div class="wiciCheckBox" >
								<input type="checkbox" name="signatureScreen_AcceptAgreementNew" id="signatureScreen_AcceptAgreementNew" />
								<label for="signatureScreen_AcceptAgreementNew"/>
							</div>
					</div>		
                   		<div class="leftContainer opCheckBoxLabel align-vertical-middle-item">
						<label data-translationKey="signatureScreen_TermsAndConditions_AcceptBoxNew" />   
						</div>
					</div>
			</div>
			
			<!-- US5147 WICI - Updated Signature Box -->
            <br/>
            <center>
            	<div class="signatureContainer sugnatureFixes" >
                	<div class="signatureBorder" id="signatureScreen_SignatureContainer">
                    	<div class="signature" id="signature" />
                    </div>
                </div>
                
                <span id="signatureScreen_Reset_Button"
                    class="button grayflat sigResetButton proceedButtonWidth">
                    <span class="buttonText" data-translationKey="signatureScreen_Reset_Button_Label"></span>
                </span>
             </center>
		</div>
		<!--<center>
            <span id="signatureScreen_ProceedButton" class="button greenflat proceedToConfirmationButtonWidth">
            	<span class="buttonText" data-translationKey="sigScreen_ProceedToConfirmation"></span>
			</span>
        </center>-->
       <div class="signaturePageBottomMargin">
		  <span id="sigworldElite_MasterCardNote_OMZ" data-translationKey="sigworldElite_MasterCardNote_1"> </span>
       </div>
		
	</div>
</script>

<script id="WICISummaryScreen-template" type="text/x-jquery-tmpl">
	<div class="ScreenLayout" id="SummaryScreen_PageContents">
	 	<br>
	 	<br>
		<br>
		<span class="pageSubTitle" data-translationKey="summary_Status_SubTitle"></span>
		<!--<span class="pageSubTitle" data-translationKey="summary_Status_NotReady" id="summary_Status_NotReady"></span> -->
		<span class="pageSubTitle" data-translationKey="summary_Status_Ready" id="summary_Status_Ready"></span>
	 	<br>
	 	<br>
		<br>
		{{if activationItems.getModel('loginScreen').get('retailNetWork') != "PRTNR" && activationItems.getModel('loginScreen').get('retailNetWork') != "OS" }}
		<div class="highlighterDIV" id="highlighterDIVPA" name="highlighterDIVPA">
				<center>
						<p class="highlighterHeader" id="highlighterHeaderPA" name="highlighterHeaderPA"><span data-translationKey="summary_highlighterHeader"/></p>
				</center>
         	<center> 
			{{if activationItems.getModel('loginScreen').get('retailNetWork') == "CT" }}			
				<span class="pageSubTitle" data-translationKey="summary_highlighter_SubTitle"></span><span class="pageSubTitle" data-translationKey="Canadian_Tire"></span><span class="pageSubTitle" data-translationKey="summary_highlighter_Representative"></span>
			{{else activationItems.getModel('loginScreen').get('retailNetWork') == "MARKS" }}
				<span class="pageSubTitle" data-translationKey="summary_highlighter_SubTitle"></span><span class="pageSubTitle" data-translationKey="Marks"></span><span class="pageSubTitle" data-translationKey="summary_highlighter_Representative"></span>
			{{else activationItems.getModel('loginScreen').get('retailNetWork') == "SPORTS" }}
				<span class="pageSubTitle" data-translationKey="summary_highlighter_SubTitle"></span><span class="pageSubTitle" data-translationKey="SportsCheck_OR_Atmosphere"></span><span class="pageSubTitle" data-translationKey="summary_highlighter_Representative"></span>
			{{else activationItems.getModel('loginScreen').get('retailNetWork') == "GAS" }}
				<span class="pageSubTitle" data-translationKey="summary_highlighter_SubTitle"></span><span class="pageSubTitle" data-translationKey="Gas"></span><span class="pageSubTitle" data-translationKey="summary_highlighter_Representative"></span>
			{{else activationItems.getModel('loginScreen').get('retailNetWork') == "FGLFRN" }}
				<span class="pageSubTitle" data-translationKey="summary_highlighter_SubTitle"></span><span class="pageSubTitle" data-translationKey="Sports_Expert"></span><span class="pageSubTitle" data-translationKey="summary_highlighter_Representative"></span>
			{{else activationItems.getModel('loginScreen').get('retailNetWork') == "PHL" }}
				<span class="pageSubTitle" data-translationKey="summary_highlighter_SubTitle"></span><span class="pageSubTitle" data-translationKey="Pro_Hockey_Life"></span><span class="pageSubTitle" data-translationKey="summary_highlighter_Representative"></span>
			{{else activationItems.getModel('loginScreen').get('retailNetWork') == "NS" }}
				<span class="pageSubTitle" data-translationKey="summary_highlighter_SubTitle"></span><span class="pageSubTitle" data-translationKey="National_Life"></span><span class="pageSubTitle" data-translationKey="summary_highlighter_Representative"></span>
			{{else activationItems.getModel('loginScreen').get('retailNetWork') == "MRKFRN" }}
				<span class="pageSubTitle" data-translationKey="summary_highlighter_SubTitle"></span><span class="pageSubTitle" data-translationKey="Marks_Franchise"></span><span class="pageSubTitle" data-translationKey="summary_highlighter_Representative"></span>
			{{else activationItems.getModel('loginScreen').get('retailNetWork') == "PC" }}
				<span class="pageSubTitle" data-translationKey="summary_highlighter_SubTitle"></span><span class="pageSubTitle" data-translationKey="Party_City"></span><span class="pageSubTitle" data-translationKey="summary_highlighter_Representative"></span>
			{{/if}}
			</center>
		</div>
        <br>
		<br>
		<br>
		{{/if}}
		<center>
			<span class="pageTitle" data-translationKey="summary_PageTitle"></span>
		</center>
		<br>
		<br>
		<div class="overlay" id="useCaseFour-container">
  			<div class="handoutTabToCustomerpopup_chooseProduct">
				{{if activationItems.getModel("financialData").get('summary_insurance_CPType_Available') == "CP_Complete" }}
    				<p class="text-left normal_text" data-translationKey="summary_UseCaseFourDialogContentCPC"></p>
				{{else activationItems.getModel("financialData").get('summary_insurance_CPType_Available') == "CP_LifeDisability" }}
					<p class="text-left normal_text" data-translationKey="summary_UseCaseFourDialogContentCPLD"></p>
				{{/if}}
				<center class="retrieveAppPadding">
					<div>      				
	      				<button class="dialog-btn-ok" id="useCaseFourOk" data-translationKey="summary_UseCaseFourDialogOk"></button>
    				</div>
				</center>
  			</div>
		</div>
		<!--<center>//-->
			<!--
				summary_SelectProduct_Card							:	"Card",
				summary_SelectProduct_PromoCode						:	"Promo Code",
				summary_SelectProduct_Province						:	"Province",
			//-->
			<div id="summary_SelectedProduct_Area" >
				<span class="pageSubTitle" data-translationKey="summary_SelectProduct_SubTitle"></span>
				<br>
				<br>
	            <table id="summary_SelectedProductTable" class="stretchThisTable">
	                <tr class="fieldRow">
	                    <td class="fieldLabelsTopCell fieldCellSize33">
	                        <span data-translationKey="summary_SelectProduct_Card"></span>
	                    </td>
	                    <td class="fieldLabelsCell fieldValuesTopCell">
	                    
	                    <!--	<span data-translationKey=${ activationItems.getCardFriendlyName(activationItems.getModel('chooseProductModel').get('productCard')) }></span> //-->
	                    
	                    {{if activationItems.getModel('chooseProductModel').get('productCard') === 'OMR' }}
							<span data-translationKey="signature_CashAdvantageMasterCard" ></span>
						{{else activationItems.getModel('chooseProductModel').get('productCard') === 'OMP' }}
							<span data-translationKey="signature_GasAdvantageMasterCard" ></span>
					    {{else activationItems.getModel('chooseProductModel').get('productCard') === 'OMZ' }}
							<span data-translationKey="signature_World_ELiteMasterCard" ></span>		
						{{else}}
							<span data-translationKey="signature_OptionsMasterCard" ></span>
						{{/if}}
	                    
	                    </td>
	                </tr>
	                <tr class="fieldRow">
	                    <td class="fieldLabelsCell fieldCellSize33">
	                        <span data-translationKey="summary_SelectProduct_PromoCode"></span>
	                    </td>
	                    <td class="fieldLabelsCell">
							<span id="promocode" data-translationKey=${activationItems.getModel('chooseProductModel').get('agencyProgram') == "BLANK"? "BLANK": /*activationItems.getModel('chooseProductModel').get('agencyPromoCode')*/ app.agencyPromoCode}></span>
	                    </td>
	                </tr>
	                <tr class="fieldRow">
	                    <td class="fieldLabelsBottomCell fieldCellSize33">
	                        <span data-translationKey="summary_SelectProduct_Province"></span>
	                    </td>
	                    <td class="fieldLabelsCell fieldValuesBottomCell">
	                         <span data-translationKey=${activationItems.getProvinceNameByValue(activationItems.getModel('chooseProductModel').get('province'))}></span>
	                    </td>
	                </tr>
	            </table>
			</div>
		<!--</center>//-->
		<!--
		personalData_CTMNumber_TextField					:	"",
	  personalData_CTMAccountText      					:	""
		//-->
        {{if activationItems.getModel('chooseProductModel').get('productCard')=='OMX' }}
        <center>
			<div id="personalData_MyCTMArea" >
			<br>
			<br>
	            <table id="myCTMTable" class="stretchThisTable">
					<tr class="fieldRow">
	                    <td class="fieldLabelsTopCell fieldCellSize33">
	                        <span data-translationKey="personalData_CTMField"></span>
	                    </td>
	                    <td class="fieldLabelsCell fieldValuesTopCell">
	                         ${activationItems.getModel('personalData').get('loyaltyMembershipNumber')}
	                    </td>
	                </tr>
	                <tr class="fieldRow">
	                    <td  style="border-left:0px;border-bottom:0px;outline-style: none;margin: 0px;">
	                    </td>
	                    <td class="fieldValuesCell">
							<p><span data-translationKey="personalData_CTMAccountText"></span></p>
	                    </td>
	                </tr>
	            </table>
			</div>
		</center>
        {{/if}}

		<!--</center>//-->
		<!--
	summary_TellAboutYourself_SubTitle					:	"Tell Us About Yourself",
	personalData_IDType					:	"ID Type",
	personalData_PlaceOfIssue				:	"Place of Issue",
	personalData_IDNumber					:	"ID Number",
	personalData_ExpiryDate					:	"ID Expiry Date", // US4364

		//-->
			<div id="summary_TellUsAboutYourself_Area1">
				<br>
				<br>
				<span class="pageSubTitle" data-translationKey="summary_TellAboutYourself_SubTitle"></span>
				<br>
				<br>
	            <table id="summary_TellUsAboutYourself_Table" class="stretchThisTable">
	                <tr class="fieldRow">
	                    <td class="fieldLabelsTopCell fieldCellSize33">
	                        <span data-translationKey="personalData_IDType"></span>
	                    </td>
	                    <td class="fieldLabelsCell fieldValuesTopCell">
	                    	<!-- US4078 //-->	                    	
	                    	 {{if activationItems.getModel('personalData').get('placeofissue').toUpperCase() === "BC" && 
	                    	 		activationItems.getModel('personalData').get('idtype').toUpperCase() === "HE" }}			
								<span data-translationKey=${activationItems.getPersonalDataIdTypeNameByValue('BS')}></span>
							{{else}}
								<span data-translationKey=${activationItems.getPersonalDataIdTypeNameByValue(activationItems.getModel('personalData').get('idtype'))}></span>
							{{/if}}	                    								
	                        <!--${ activationItems.getModel('personalData').get('idtype') } //-->
	                    </td>
	                </tr>
	                <tr class="fieldRow">
	                    <td class="fieldLabelsCell fieldCellSize33">
	                        <span data-translationKey="personalData_PlaceOfIssue"></span>
	                    </td>
	                    <td class="fieldLabelsCell">
	                    	<span data-translationKey=${activationItems.getProvinceNameByValue(activationItems.getModel('personalData').get('placeofissue'))}></span>
	                    	<!--${activationItems.getModel('personalData').get('placeofissue')} -->
	                    </td>
	                </tr>
	                <tr class="fieldRow">
	                    <td id="personalData_IDNumber_Label" class="fieldLabelsCell fieldCellSize33">
	                        <span data-translationKey="personalData_IDNumber"></span>
	                    </td>
	                    <td id="personalData_IDNumber_Value" class="fieldLabelsCell">
	                         ${activationItems.getModel('personalData').get('idnumbers')}
	                    </td>
	                </tr>
	                
	                <!-- US4365 -->
	                <tr id="summary_TellUsAboutYourself_ExpiryDate_Area" class="fieldRow">
	                    <td id="personalData_ExpiryDate" class="fieldLabelsBottomCell fieldCellSize33">
	                        <span data-translationKey="personalData_ExpiryDate"></span>
	                    </td>
	                    <td id="expiryDate_id" class="fieldValuesBottomCell">
                              ${activationItems.getFormatedDate(activationItems.getModel('personalData').get('idExpiryDate'))}
	                    </td>
	                </tr>
	                
	            </table>
			</div>

<!--
	personalData_EmailAddress                           :   "Email Address",
	summary_TellAboutYourself_Email_Consent             :   "E-mail Marketing Consent?",
	summary_TellAboutYourself_HomePhone					:	"Home Phone",
	summary_TellAboutYourself_CellPhone					:	"Cellular Phone",
		//-->

	<div id="summary_TellUsAboutYourself_Area1">
				<br>
				<br>
				<span class="pageSubTitle" data-translationKey="summary_ContactInfo_SubTitle"></span>
				<br>
				<br>
	            <table id="summary_TellUsAboutYourself_Table" class="stretchThisTable">
	                <tr class="fieldRow">
	                    <td class="fieldLabelsTopCell fieldCellSize33">
	                        <span data-translationKey="personalData_PrimaryPhone"></span>
	                    </td>
	                    <td class="fieldLabelsCell fieldValuesTopCell">
							${activationItems.getFormatedPhone(activationItems.getModel('contactInfoScreen').get('homePhone'))}
	                    </td>
	                </tr>
	                <tr class="fieldRow">
	                    <td class="fieldLabelsCell fieldCellSize33">
	                        <span data-translationKey="personalData_SecondaryPhone"></span>
	                    </td>
	                    <td class="fieldLabelsCell">
	                    	${activationItems.getFormatedPhone(activationItems.getModel('contactInfoScreen').get('cellPhone'))}
	                    </td>
	                </tr>
	                <tr class="fieldRow">
	                    <td id="personalData_IDNumber_Label" class="fieldLabelsCell fieldCellSize33">
	                        <span data-translationKey="personalData_EmailAddress"></span>
	                    </td>
	                    <td id="personalData_IDNumber_Value" class="fieldLabelsCell">
	                       {{if activationItems.getModel('contactInfoScreen').get('email') == "" }}			
								<span data-translationKey="ContactInfo_email_summary"></span>
							{{else}}
								 ${activationItems.getModel('contactInfoScreen').get('email')}
							{{/if}}	
	                    </td>
	                </tr>
	                <tr class="fieldRow">
	                    <td  class="fieldLabelsCell fieldCellSize33">
	                        <span data-translationKey="summary_OptProds_Signature"></span>
	                    </td>
	                    <td  class="fieldLabelsCell">
	                        <img src = ${activationItems.getModel('contactInfoScreen').get('emailConfirmationSignature')} class="signatureSummary" />
	                    </td>
	                </tr>
					
	                <tr id="summary_TellUsAboutYourself_ExpiryDate_Area" class="fieldRow">
	                    <td id="personalData_ExpiryDate" class="fieldLabelsBottomCell fieldCellSize33">
	                        <span data-translationKey="summary_TellAboutYourself_Email_Consent"></span>
	                    </td>
	                    <td id="expiryDate_id" class="fieldValuesBottomCell fieldLabelsCell">
                              {{if activationItems.getModel('contactInfoScreen').get('email') !== null}}
	                              <span data-translationKey="${ activationItems.getModel('contactInfoScreen').get('receiveEmail') == "Y" ? "yes" : activationItems.getModel('contactInfoScreen').get('receiveEmail') == "OPERATIONAL"? "no" : "" }"></span>
	                              {{/if}}
	                    </td>
	                </tr>
	                
	            </table>
			</div>

<!--
	summary_TellAboutYourself_ApplicantInfo             :   "Applicant Information",
    personalData_Title                                  : 	"Title",
	summary_TellAboutYourself_FirstName					:	"First Name",
	summary_TellAboutYourself_Initial					:	"Initial",
	summary_TellAboutYourself_LastName					:	"Last Name",
	summary_TellAboutYourself_DateOfBirth				:	"Date of Birth",
	summary_TellAboutYourself_Correspondence			:	"Correspondence",
//-->
            <br>
				<br>
				<span class="pageSubTitle" data-translationKey="summary_TellAboutYourself_ApplicantInfo"></span>
				<br>
			<div id="summary_TellUsAboutYourself_Area2" >
				<br>
	            <table id="summary_TellUsAboutYourself_Table" class="stretchThisTable">
	                <tr class="fieldRow">
	                    <td class="fieldLabelsTopCell fieldCellSize33">
	                        <span data-translationKey="summary_TellAboutYourself_FirstName"></span>
	                    </td>
	                    <td class="fieldLabelsCell fieldValuesTopCell">
	                        ${activationItems.getModel('personalData').get('firstName')}
	                    </td>
	                </tr>
	                <tr class="fieldRow">
	                    <td class="fieldLabelsCell fieldCellSize33">
	                        <span data-translationKey="summary_TellAboutYourself_Initial"></span>
	                    </td>
	                    <td class="fieldLabelsCell">
	                         ${activationItems.getModel('personalData').get('initial')}
	                    </td>
	                </tr>
	                <tr class="fieldRow">
	                    <td class="fieldLabelsCell fieldCellSize33">
	                        <span data-translationKey="summary_TellAboutYourself_LastName"></span>
	                    </td>
	                    <td class="fieldLabelsCell">
	                         ${activationItems.getModel('personalData').get('lastName')}
	                    </td>
	                </tr>
	                <tr class="fieldRow">
	                    <td class="fieldLabelsCell fieldCellSize33">
	                        <span data-translationKey="summary_TellAboutYourself_DateOfBirth"></span>
	                    </td>
	                    <td id="birthDate_id_1" class="fieldLabelsCell">
	                    	 ${activationItems.getFormatedDate(activationItems.getModel('personalData').get('birthDate'))}
	                         <!--${activationItems.getModel('personalData').get('birthDate')} -->
	                    </td>
	                </tr>
	                <tr class="fieldRow">
	                    <td class="fieldLabelsBottomCell fieldCellSize33">
	                        <span data-translationKey="summary_TellAboutYourself_Correspondence"></span>
	                    </td>
	                    <td class="fieldLabelsCell fieldValuesBottomCell">
	                    	<span data-translationKey=${activationItems.getCorrespondenceFriendlyName(activationItems.getModel('personalData').get('correspondence'))}></span>
	                         <!--${activationItems.getModel('personalData').get('correspondence')} -->
	                    </td>
	                </tr>
	            </table>
			</div>
<!--
	summary_Address_SubTitle							:	"Address Information",

	summary_Address_SuiteUnit							:	"Suite/Unit",
	summary_Address_AddressLine1						:	"Mailing Address Line 1",
	summary_Address_AddressLine2						:	"Mailing Address Line 2",
	summary_Address_City								:	"City",
	summary_Address_Province							:	"Province",
	summary_Address_PostalCode							:	"Postal Code",
	summary_Address_ResidenceType						:	"Residence Type",
	summary_Address_MonthlyHousePayment					:	"Monthly House Payment",
	summary_Address_DurationCurrentAddress				:	"Duration at Current Address",

//-->

			<div id="summary_Address_Area1" >
				<br>
				<br>
				<span class="pageSubTitle" data-translationKey="summary_Address_SubTitle"></span>
				<br>
				<br>
	            <table id="summary_Address_Table" class="stretchThisTable">
					<tr class="fieldRow">
	                    <td class="fieldLabelsTopCell fieldCellSize33">
	                        <span data-translationKey="summary_Address_SuiteUnit"></span>
	                    </td>
	                    <td class="fieldLabelsCell fieldValuesTopCell">
	                         ${activationItems.getModel('personalData2_Address').get('suiteunit')}
	                    </td>
	                </tr>
	                <tr class="fieldRow">
	                    <td class="fieldLabelsCell fieldCellSize33">
	                        <span data-translationKey="summary_Address_AddressLine1"></span>
	                    </td>
	                    <td class="fieldLabelsCell">
	                         ${activationItems.getModel('personalData2_Address').get('addressline1')}
	                    </td>
	                </tr>
	                <tr id="summary_AddressLine2" class="fieldRow">
	                    <td class="fieldLabelsCell fieldCellSize33">
	                        <span data-translationKey="summary_Address_AddressLine2"></span>
	                    </td>
	                    <td class="fieldLabelsCell">
	                         ${activationItems.getModel('personalData2_Address').get('addressline2')}
	                    </td>
	                </tr>
	                <tr class="fieldRow">
	                    <td class="fieldLabelsCell fieldCellSize33">
	                        <span data-translationKey="summary_Address_City"></span>
	                    </td>
	                    <td class="fieldLabelsCell">
	                         ${activationItems.getModel('personalData2_Address').get('city')}
	                    </td>
	                </tr>
	                <tr class="fieldRow">
	                    <td class="fieldLabelsCell fieldCellSize33">
	                        <span data-translationKey="summary_Address_Province"></span>
	                    </td>
	                    <td class="fieldLabelsCell">
	                         <span data-translationKey=${activationItems.getProvinceNameByValue(activationItems.getModel('personalData2_Address').get('province'))}></span>
	                         <!--${activationItems.getModel('personalData2_Address').get('province')} -->
	                    </td>
	                </tr>
					<tr class="fieldRow">
	                    <td class="fieldLabelsCell fieldCellSize33">
	                        <span data-translationKey="summary_Address_PostalCode"></span>
	                    </td>
	                    <td class="fieldLabelsCell">
	                        ${ activationItems.getModel('personalData2_Address').get('postalcode') }
	                    </td>
	                </tr>
	                <tr class="fieldRow">
	                    <td class="fieldLabelsCell fieldCellSize33">
	                        <span data-translationKey="summary_Address_ResidenceType"></span>
	                    </td>
	                    <td class="fieldLabelsCell">
	                         <span data-translationKey=${activationItems.getResidenceTypeFriendlyName(activationItems.getModel('personalData2_Address').get('house'))}></span>
	                         <!--${activationItems.getModel('personalData2_Address').get('house')} -->
	                    </td>
	                </tr>
	                <tr class="fieldRow">
	                    <td class="fieldLabelsCell fieldCellSize33">
	                        <span data-translationKey="summary_Address_MonthlyHousePayment"></span>
	                    </td>
	                    <td class="fieldLabelsCell">
	                         <span id="summary_housingpayment"></span>
                    </td>
	                </tr>
	                <tr class="fieldRow">
	                    <td class="fieldLabelsBottomCell fieldCellSize33">
	                        <span data-translationKey="summary_Address_DurationCurrentAddress"></span>
	                    </td>
	                    <td class="fieldLabelsCell fieldValuesBottomCell">
	                         ${activationItems.getDurationValue(activationItems.getModel('personalData2_Address').get('years'))} <span data-translationKey="summary_Years" />,
							 ${activationItems.getDurationValue(activationItems.getModel('personalData2_Address').get('months'))} <span data-translationKey="summary_Months" />
	                         <!--${activationItems.getModel('personalData2_Address').get('years')} -->
	                    </td>
	                </tr>
	            </table>
			</div>
		<!--
	summary_PreviousAddress_SubTitle					:	"Previous Address Information",

	summary_Address_SuiteUnit							:	"Suite/Unit",
	summary_Address_AddressLine1						:	"Mailing Address Line 1",
	summary_Address_AddressLine2						:	"Mailing Address Line 2",
	summary_Address_City								:	"City",
	summary_Address_Province							:	"Province",
	summary_Address_PostalCode							:	"Postal Code",
		//-->
		{{if activationItems.getModel('personalData2_Address').get('years')<2 }}
			<div id="summary_Address_Area2" >
				<br>
				<br>
				<span class="pageSubTitle" data-translationKey="summary_PreviousAddress_SubTitle"></span>
				<br>
				<br>
	            <table id="summary_Address_Table" class="stretchThisTable">
					<tr class="fieldRow">
	                    <td class="fieldLabelsTopCell fieldCellSize33">
	                        <span data-translationKey="summary_Address_SuiteUnit"></span>
	                    </td>
	                    <td class="fieldLabelsCell fieldValuesTopCell">
	                         ${activationItems.getModel('personalData2_Address').get('suiteunit_prev')}
	                    </td>
	                </tr>
					<tr class="fieldRow">
	                    <td class="fieldLabelsCell fieldCellSize33">
	                        <span data-translationKey="summary_Address_AddressLine1"></span>
	                    </td>
	                    <td class="fieldLabelsCell">
	                         ${activationItems.getModel('personalData2_Address').get('addressline1_prev')}
	                    </td>
	                </tr>
	                <tr id="summary_PreviousAddressLine2" class="fieldRow">
	                    <td class="fieldLabelsCell fieldCellSize33">
	                        <span data-translationKey="summary_Address_AddressLine2"></span>
	                    </td>
	                    <td class="fieldLabelsCell">
	                         ${activationItems.getModel('personalData2_Address').get('addressline2_prev')}
	                    </td>
	                </tr>
					<tr class="fieldRow">
	                    <td class="fieldLabelsCell fieldCellSize33">
	                        <span data-translationKey="summary_Address_City"></span>
	                    </td>
	                    <td class="fieldLabelsCell">
	                         ${activationItems.getModel('personalData2_Address').get('city_prev')}
	                    </td>
	                </tr>
					<tr class="fieldRow">
	                    <td class="fieldLabelsCell fieldCellSize33">
	                        <span data-translationKey="summary_Address_Province"></span>
	                    </td>
	                    <td class="fieldLabelsCell">
	                         <span data-translationKey=${activationItems.getProvinceNameByValue(activationItems.getModel('personalData2_Address').get('province_prev'))}></span>
	                    </td>
	                </tr>
	                <tr class="fieldRow">
	                    <td class="fieldLabelsBottomCell fieldCellSize33">
	                        <span data-translationKey="summary_Address_PostalCode"></span>
	                    </td>
	                    <td class="fieldLabelsCell fieldValuesBottomCell">
	                        ${ activationItems.getModel('personalData2_Address').get('postalcode_prev') }
	                    </td>
	                </tr>
	            </table>
			</div>
		{{/if}}
<!--
	summary_FinEmp_SubTitle1							:	"Financial Information and ",
	summary_FinEmp_SubTitle2							:	"Employment Information",
	summary_FinEmp_BankingProducts						:	"Banking Products",

	summary_FinEmp_EmploymentType						:	"Employment Type",
	summary_FinEmp_GrossAnnualIncome					:	"Gross Annual Income",
	summary_FinEmp_SIN									:	"Social Insurance Number",

	summary_FinEmp_JobDescription						:	"Job Description",
	summary_FinEmp_JobCategory							:	"Job Category",
	summary_FinEmp_EmpName								:	"Employer Name",
	summary_FinEmp_EmpCity								:	"Employer City",
	summary_FinEmp_EmpProvince							:	"Employer Province",
	summary_FinEmp_EmpPhone								:	"Employer Phone",
	summary_FinEmp_DurationCurrentEmp					:	"Duration at Current Employer",
//-->
			<div id="summary_FinEmp_Area1" >
				<br>
				<br>
				<span class="pageSubTitle" data-translationKey="summary_FinEmp_SubTitle1"></span><span class="pageSubTitle" data-translationKey="summary_FinEmp_SubTitle2"></span>
				<br>
				<br>
	            <table id="summary_Address_Table" class="stretchThisTable">
	               <!-- <tr class="fieldRow">
	                    <td class="fieldLabelsTopCell fieldCellSize33">
	                        <span data-translationKey="summary_FinEmp_BankingProducts"></span>
	                    </td>
	                    <td class="fieldLabelsCell fieldValuesTopCell">
	                        <span data-translationKey="finEmpInfo_VISAMCAMEX"></span>: &nbsp <span data-translationKey=${activationItems.getCheckBoxValueFriendlyName(activationItems.getModel('financialData').get('cardVISAMCAMEX'))}></span>, &nbsp
	                        <span data-translationKey="finEmpInfo_BankLoan"></span>: &nbsp <span data-translationKey=${activationItems.getCheckBoxValueFriendlyName(activationItems.getModel('financialData').get('cardBankLoan'))}></span>, &nbsp
	                        <span data-translationKey="finEmpInfo_StoreCard"></span>: &nbsp <span data-translationKey=${activationItems.getCheckBoxValueFriendlyName(activationItems.getModel('financialData').get('cardStoreCard'))}></span>, &nbsp
	                        <span data-translationKey="finEmpInfo_ChequingAcct"></span>: &nbsp <span data-translationKey=${activationItems.getCheckBoxValueFriendlyName(activationItems.getModel('financialData').get('cardChequingAcct'))}></span>, &nbsp
	                        <span data-translationKey="finEmpInfo_GasCard"></span>: &nbsp <span data-translationKey=${activationItems.getCheckBoxValueFriendlyName(activationItems.getModel('financialData').get('cardGasCard'))}></span>, &nbsp
	                        <span data-translationKey="finEmpInfo_SavingsAcct"></span>: &nbsp <span data-translationKey=${activationItems.getCheckBoxValueFriendlyName(activationItems.getModel('financialData').get('cardSavingsAcct'))}></span>
	                    </td>
	                </tr>-->
	                <tr class="fieldRow">
	                    <td class="fieldLabelsTopCell fieldCellSize33">
	                        <span data-translationKey="summary_FinEmp_EmploymentType"></span>
	                    </td>
	                    <td class="fieldLabelsCell fieldValuesTopCell">
	                        <span data-translationKey=${activationItems.getEmploymentTypeFriendlyName(activationItems.getModel('financialData').get('employmentType'))}></span>
	                    </td>
	                </tr>
	                <tr class="fieldRow">
	                    <td class="fieldLabelsCell fieldCellSize33">
	                        <span data-translationKey="summary_FinEmp_GrossAnnualIncome"></span>
	                    </td>
	                    <td  class="fieldLabelsCell">
								<span id="summary_grossIncome" ></span>
	                    </td>
	                </tr>
	                <!-- US3960 Start -->
	                <tr class="fieldRow">
	                    <td class="fieldLabelsCell fieldCellSize33">
	                        <span data-translationKey="summary_FinEmp_GrossAnnualHouseholdIncome"></span>
	                    </td>
	                    <td  class="fieldLabelsCell">
								<span id="summary_grossHouseholdIncome" ></span>
	                    </td>
	                </tr>
	                <!-- End -->

	                <tr class="fieldRow">
	                    {{if activationItems.getModel('financialData').get('employmentType')=='R' }}
	                    	<td class="fieldLabelsCell fieldCellSize33">
	                    {{/if}}
	                    {{if activationItems.getModel('financialData').get('employmentType')!='R' }}
	                    	<td class="fieldLabelsCell fieldCellSize33">
	                    {{/if}}
	                        <span data-translationKey="summary_FinEmp_SIN"></span>
	                    </td>
	                    {{if activationItems.getModel('financialData').get('employmentType')=='R' }}
	                    	<td class="fieldLabelsCell fieldLabelsCell">
	                    {{/if}}
	                    {{if activationItems.getModel('financialData').get('employmentType')!='R' }}
	                    	<td class="fieldLabelsCell">
	                    {{/if}}
	                         ${activationItems.getModel('financialData').get('sin')}
	                    </td>
	                </tr>
					<tr class="fieldRow">
			            <td class="fieldLabelsCell fieldCellSize33">
			                <span data-translationKey="summary_FinEmp_JobDescription"></span>
			            </td>
			            <td class="fieldLabelsCell">
			            	{{if activationItems.getModel('financialData').get('employmentType') !='R' && activationItems.getModel('financialData').get('employmentType') !='U' && activationItems.getModel('financialData').get('employmentType') !='H'}}			            					               		
			               		<span id="summary_JobDescription"></span>			               		
							{{else}}
			                 	<span data-translationKey=${activationItems.getJobCategoryNameByValue(activationItems.getModel('financialData').get('jobCategory'))}></span>
			                {{/if}}
			            </td>
			        </tr>
					{{if activationItems.getModel('financialData').get('jobDescriptionTemp').toUpperCase() == "OTHER" || activationItems.getModel('financialData').get('jobDescriptionTemp').toUpperCase() == "AUTRE" }}
					<tr class="fieldRow">
			            <td class="fieldLabelsCell fieldCellSize33">
			                <span data-translationKey="summary_FinEmp_JobDescriptionOther"></span>
			            </td>
			            <td class="fieldLabelsCell">
			            	{{if activationItems.getModel('financialData').get('employmentType') !='R' && activationItems.getModel('financialData').get('employmentType') !='U' && activationItems.getModel('financialData').get('employmentType') !='H'}}			            		
			               		${activationItems.getModel('financialData').get('jobDescriptionOther')}
							{{else}}			                 	
			                 	<span data-translationKey=${activationItems.getJobCategoryNameByValue(activationItems.getModel('financialData').get('jobCategory'))}></span>
			                {{/if}}
			            </td>
			        </tr>					
					{{/if}}
	                
			        <tr class="fieldRow">
			       		{{if  activationItems.getModel('financialData').get('employmentType')=='R' || activationItems.getModel('financialData').get('employmentType')=='U' || activationItems.getModel('financialData').get('employmentType')=='H'}}
	                    	<td class="fieldLabelsBottomCell fieldCellSize33">
	                    {{else}}
	                    	<td class="fieldLabelsCell fieldCellSize33">
	                    {{/if}}
			                <span data-translationKey="summary_FinEmp_JobCategory"></span>
			            </td>
			            {{if  activationItems.getModel('financialData').get('employmentType')=='R' || activationItems.getModel('financialData').get('employmentType')=='U' || activationItems.getModel('financialData').get('employmentType')=='H'}}
	                    	<td class="fieldLabelsCell fieldValuesBottomCell">
	                    {{else}}
	                    	<td class="fieldLabelsCell fieldCellSize33">
	                    {{/if}}
			                <span data-translationKey=${activationItems.getJobCategoryNameByValue(activationItems.getModel('financialData').get('jobCategory'))}></span>
			                <!--${activationItems.getModel('financialData').get('jobCategory')} -->
			            </td>
			        </tr>

	                {{if activationItems.getModel('financialData').get('employmentType')!='R' && activationItems.getModel('financialData').get('employmentType')!='U' && activationItems.getModel('financialData').get('employmentType')!='H' }}
						<tbody>
			                <tr class="fieldRow">
			                    <td class="fieldLabelsCell fieldCellSize33 withBorderTop">
			                        <span data-translationKey="summary_FinEmp_EmpName"></span>
			                    </td>
			                    <td class="fieldLabelsCell withBorderTop">
			                         ${activationItems.getModel('financialData').get('employerName')}
			                    </td>
			                </tr>
			                <tr class="fieldRow">
			                    <td class="fieldLabelsCell fieldCellSize33">
			                        <span data-translationKey="summary_FinEmp_EmpCity"></span>
			                    </td>
			                    <td class="fieldLabelsCell">
			                         ${activationItems.getModel('financialData').get('employerCity')}
			                    </td>
			                </tr>

			                <tr class="fieldRow">
			                    <td class="fieldLabelsCell fieldCellSize33">
			                        <span data-translationKey="summary_FinEmp_EmpPhone"></span>
			                    </td>
			                    <td class="fieldLabelsCell">
			                         ${activationItems.getFormatedPhone(activationItems.getModel('financialData').get('employerPhone'))}
			                         <!--${activationItems.getModel('financialData').get('employerPhone')}-->
			                    </td>
			                </tr>
			                <tr class="fieldRow">
			                    <td class="fieldLabelsBottomCell fieldCellSize33">
			                        <span data-translationKey="summary_FinEmp_DurationCurrentEmp"></span>
			                    </td>
			                    <td class="fieldLabelsCell fieldValuesBottomCell">
			                         ${activationItems.getDurationValue(activationItems.getModel('financialData').get('howLongYears'))} <span data-translationKey="summary_Years" />,
							 		 ${activationItems.getDurationValue(activationItems.getModel('financialData').get('howLongMonthes'))} <span data-translationKey="summary_Months" />
			                    </td>
			                </tr>
		                <tbody>
	                {{/if}}
	            </table>
			</div>
<!--
	summary_SupCard_SubTitle							:	"Supplementary Card Information",
	summary_SupCard_OptedIn								:	"Wants supplementary card",
	summary_SupCard_ForWhom								:	"Supplementary card for",
	personalData_FirstName								:	"First Name",
	personalData_Initial								:	"Initial",
	personalData_LastName								:	"Last Name",
	personalData_DateOfBirth							:	"Date of Birth",
	summary_SupCard_Telephone							:	"Telephone",

	summary_SupCard_Address_SubTitle					:	"Address Information",

	summary_SupCard_Address_SuiteUnit					:	"Suite/Unit",
	summary_SupCard_Address_AddressLine1				:	"Address Line 1",
	summary_SupCard_Address_AddressLine2				:	"Address Line 2",
	summary_SupCard_Address_City						:	"City",
	summary_Address_Province                            :   "Province",
	summary_SupCard_Address_PostalCode					:	"Postal Code",
//-->
			<div id="summary_SupCard_Area1" >
				<br>
				<br>
				<span class="pageSubTitle" data-translationKey="summary_SupCard_SubTitle"></span>
				<br>
				<br>
	            <table id="summary_Address_Table" class="stretchThisTable">
	            	<tbody>
		                <tr class="fieldRow">
		                    {{if activationItems.getModel('supCardRequestData').get('cardYesNo')==='Y' }}
		                    	<td class="fieldLabelsTopCell fieldCellSize33">
		                    {{/if}}
		                    {{if activationItems.getModel('supCardRequestData').get('cardYesNo')==='N' }}
		                    	<td class="fieldLabelsSingleCell fieldCellSize33">
		                   	{{/if}}
		                        <span data-translationKey="summary_SupCard_OptedIn"></span>
		                    </td>
		                    {{if activationItems.getModel('supCardRequestData').get('cardYesNo')==='Y' }}
		                    	<td class="fieldLabelsCell fieldValuesTopCell">
		                    {{/if}}
		                    {{if activationItems.getModel('supCardRequestData').get('cardYesNo')==='N' }}
		                    	<td class="fieldLabelsCell fieldValuesOneCell">
		                    {{/if}}
		                        <span data-translationKey=${activationItems.getCheckBoxValueFriendlyName(activationItems.getModel('supCardRequestData').get('cardYesNo')) }></span>
		                        <!--${activationItems.getModel('supCardRequestData').get('cardYesNo')} -->
		                    </td>
		                </tr>
                        {{if activationItems.getModel('supCardRequestData').get('cardYesNo')==='Y' }}
                            <tr class="fieldRow">
                                <td class="fieldLabelsCell fieldCellSize33">
                                    <span data-translationKey="summary_SupCard_ForWhom"></span>
                                </td>
                                <td class="fieldLabelsCell">
                                    <span data-translationKey=${activationItems.getSupCardForFriendlyNames(activationItems.getModel('supCardRequestData').get('cardRequestFor')) }></span>
                                    <!--${activationItems.getModel('supCardRequestData').get('cardRequestFor')}-->
                                </td>
                            </tr>
                            <tr class="fieldRow">
                                <td class="fieldLabelsCell fieldCellSize33">
                                    <span data-translationKey="personalData_FirstName"></span>
                                </td>
                                <td class="fieldLabelsCell">
                                     ${activationItems.getModel('supCardRequestData').get('firstName')}
                                </td>
                            </tr>
                            <tr class="fieldRow">
                                <td class="fieldLabelsCell fieldCellSize33">
                                    <span data-translationKey="personalData_Initial"></span>
                                </td>
                                <td class="fieldLabelsCell">
                                     ${activationItems.getModel('supCardRequestData').get('initial')}
                                </td>
                            </tr>
                            <tr class="fieldRow">
                                <td class="fieldLabelsCell fieldCellSize33">
                                    <span data-translationKey="personalData_LastName"></span>
                                </td>
                                <td class="fieldLabelsCell">
                                     ${activationItems.getModel('supCardRequestData').get('lastName')}
                                </td>
                            </tr>
                            <tr class="fieldRow">
                                <td class="fieldLabelsCell fieldCellSize33">
                                    <span data-translationKey="personalData_DateOfBirth"></span>
                                </td>
                                <td id="birthDate_id_2" class="fieldLabelsCell">
                                     ${activationItems.getFormatedDate(activationItems.getModel('supCardRequestData').get('birthDate'))}
                                     <!--${activationItems.getModel('supCardRequestData').get('birthDate')} -->
                                </td>
                            </tr>
                            <tr class="fieldRow">
                                <td class="fieldLabelsCell fieldCellSize33">
                                    <span data-translationKey="summary_SupCard_Telephone"></span>
                                </td>
                                <td class="fieldLabelsCell">
                                     ${activationItems.getFormatedPhone(activationItems.getModel('supCardRequestData').get('phone'))}
                                     <!-- ${activationItems.getModel('supCardRequestData').get('phone')} -->
                                </td>
                            </tr>

                            <tr class="fieldRow">
                                {{if activationItems.getModel('supCardRequestData').get('sameAddressYesNo')==='Y' }}
                                    <td class="fieldLabelsBottomCell fieldCellSize33">
                                {{/if}}
                                {{if activationItems.getModel('supCardRequestData').get('sameAddressYesNo')==='N' }}
                                    <td class="fieldLabelsCell fieldCellSize33">
                                {{/if}}
                                    <span data-translationKey="supCardRequest_SameAddyPrimaryApplicant"></span>
                                </td>
                                {{if activationItems.getModel('supCardRequestData').get('sameAddressYesNo')==='Y' }}
                                    <td class="fieldLabelsCell fieldValuesBottomCell">
                                {{/if}}
                                {{if activationItems.getModel('supCardRequestData').get('sameAddressYesNo')==='N' }}
                                    <td class="fieldLabelsCell fieldCellSize33">
                                {{/if}}
                                    <span data-translationKey=${activationItems.getCheckBoxValueFriendlyName(activationItems.getModel('supCardRequestData').get('sameAddressYesNo')) }></span>
                                </td>
                            </tr>

                            {{if activationItems.getModel('supCardRequestData').get('sameAddressYesNo')==='N' }}
                            
                            <tr class="fieldRow">
                                <td class="fieldLabelsCell fieldCellSize33">
                                    <span data-translationKey="summary_SupCard_Address_SuiteUnit"></span>
                                </td>
                                <td class="fieldLabelsCell">
                                     ${activationItems.getModel('supCardRequestData').get('suiteUnit')}
                                </td>
                            </tr>
							<tr class="fieldRow">
                                <td class="fieldLabelsCell fieldCellSize33">
                                    <span data-translationKey="summary_SupCard_Address_AddressLine1"></span>
                                </td>
                                <td class="fieldLabelsCell">
                                     ${activationItems.getModel('supCardRequestData').get('addressLine1')}
                                </td>
                            </tr>
                            <tr id="summary_SupAddressLine2" class="fieldRow">
                                <td class="fieldLabelsCell fieldCellSize33">
                                    <span data-translationKey="summary_SupCard_Address_AddressLine2"></span>
                                </td>
                                <td class="fieldLabelsCell">
                                     ${activationItems.getModel('supCardRequestData').get('addressLine2')}
                                </td>
                            </tr>
                            <tr class="fieldRow">
                                <td class="fieldLabelsCell fieldCellSize33">
                                    <span data-translationKey="summary_SupCard_Address_City"></span>
                                </td>
                                <td class="fieldLabelsCell">
                                     ${activationItems.getModel('supCardRequestData').get('city')}
                                </td>
                            </tr>
                            <tr class="fieldRow">
                                <td class="fieldLabelsCell fieldCellSize33">
                                    <span data-translationKey="summary_Address_Province"></span>
                                </td>
                                <td class="fieldLabelsCell">
                                    <span data-translationKey=${activationItems.getProvinceNameByValue(activationItems.getModel('supCardRequestData').get('province'))}></span>
                                </td>
                            </tr>
							<tr class="fieldRow">
                                <td class="fieldLabelsBottomCell fieldCellSize33">
                                    <span data-translationKey="summary_SupCard_Address_PostalCode"></span>
                                </td>
                                <td class="fieldLabelsCell fieldValuesBottomCell">
                                     ${activationItems.getModel('supCardRequestData').get('postalCode')}
                                </td>
                            </tr>
                            {{/if}}
                        {{/if}}
					</tbody>
	            </table>
			</div>

			<div id="summary_Signature_Area" >
				<br>
				<br>
				<span class="pageSubTitle" data-translationKey="summary_Signature_SubTitle"></span>
				<br>
				<br>
	            <table id="summary_SignatureProductTable" class="stretchThisTable">
	                <tr class="fieldRow">
	                    <td class="fieldLabelsTopCell fieldCellSize33">
	                        <span data-translationKey="summary_Signature_Signature"></span>
	                    </td>
	                    <td class="fieldLabelsCell fieldValuesTopCell">
	                        <img src=${activationItems.getModel('signatureModel').get('userSignature') } class="signatureSummary" />

	                    </td>
	                </tr>

	                <tr class="fieldRow">
	                    <td class="fieldLabelsCell fieldCellSize33">
	                        <span data-translationKey="summary_Signature_Accept"></span>
	                    </td>
	                    <td class="fieldLabelsCell">
	                        <span data-translationKey=${activationItems.getCheckBoxValueFriendlyName(activationItems.getModel('signatureModel').get('userAcceptAgreement')) }></span>
	                    </td>
	                </tr>

	                <tr class="fieldRow">
	                    <td class="fieldLabelsBottomCell fieldCellSize33">
	                        <span data-translationKey="summary_Signature_SignDate"></span>
	                    </td>
	                    <td id="signDate_id_1" class="fieldLabelsCell fieldValuesBottomCell">
	                        ${activationItems.getFormatedDate(activationItems.getModel('signatureModel').get('signDate'))}
	                        <!--${activationItems.getModel('signatureModel').get('signDate') } -->
	                    </td>
	                </tr>

	            </table>
			</div>
			{{if isNotEmployee}}
			<div id="summary_OptProductBenefit_Area" >

				<br>
				<br>
				<span class="pageSubTitle" data-translationKey="breadCrumbItem_OptionalProducts"></span>
				<br>
				<br>
				<!--  Start Credit Protector Life and Disability case -->
	            {{if activationItems.getModel('OptionalProductsModel').get('optionalProducts_CPLD')==='Y' }}
					<table id="summary_SelectedProductTable" class="stretchThisTable">
		                <tbody>
			                <tr class="fieldRow">
			                    <td class="fieldLabelsTopCell fieldCellSize33">
			                        <span data-translationKey="summary_OptProds_ProductSelected"></span>
			                    </td>
			                    <td class="fieldLabelsCell fieldValuesTopCell">
									<span data-translationKey="summary_OptProds_ProdName_CPLD"></span>
			                    </td>
			                </tr>

			                <tr class="fieldRow">
			                    <td class="fieldLabelsCell fieldCellSize33">
			                        <span data-translationKey="summary_OptProds_Signature"></span>
			                    </td>
			                    <td class="fieldLabelsCell">
			                        <img src=${activationItems.getModel('OptionalProductsModel').get('userSignature_CPLD') } class="signatureSummary" />

			                    </td>
			                </tr>

			                <tr class="fieldRow">
			                    <td class="fieldLabelsCell fieldCellSize33">
			                        <span data-translationKey="summary_OptProds_Accept"></span>
			                    </td>
			                    <td class="fieldLabelsCell">
			                        <span data-translationKey="yes"></span>
			                    </td>
			                </tr>

			                <tr class="fieldRow">
			                    <td class="fieldLabelsBottomCell fieldCellSize33">
			                        <span data-translationKey="summary_OptProds_SignDate"></span>
			                    </td>
			                    <td id="signDate_id_2" class="fieldLabelsCell fieldValuesBottomCell">
			                        ${activationItems.getFormatedDate(activationItems.getModel('signatureModel').get('signDate'))}
			                    </td>
			                </tr>
		                </tbody>
					   </table>
					 {{/if}}
			<!--  End Credit Protector Life and Disability case -->
			<!--  Start Credit Protector Complete case -->
				{{if activationItems.getModel('OptionalProductsModel').get('optionalProducts_CPC')==='Y' }}
				 <table id="summary_SelectedProductTable" class="stretchThisTable">
		                <tbody>

			                <tr class="fieldRow">
			                    <td class="fieldLabelsTopCell fieldCellSize33">
			                        <span data-translationKey="summary_OptProds_ProductSelected"></span>
			                    </td>
			                    <td class="fieldLabelsCell fieldValuesTopCell">
									<span data-translationKey="summary_OptProds_ProdName_CPC"></span>
			                    </td>
			                </tr>

			                <tr class="fieldRow">
			                    <td class="fieldLabelsCell fieldCellSize33">
			                        <span data-translationKey="summary_OptProds_Signature"></span>
			                    </td>
			                    <td class="fieldLabelsCell">
			                        <img src=${activationItems.getModel('OptionalProductsModel').get('userSignature_CPC') } class="signatureSummary" />

			                    </td>
			                </tr>

			                <tr class="fieldRow">
			                    <td class="fieldLabelsCell fieldCellSize33">
			                        <span data-translationKey="summary_OptProds_Accept"></span>
			                    </td>
			                    <td class="fieldLabelsCell">
			                       <span data-translationKey="yes"></span>
			                    </td>
			                </tr>

			                <tr class="fieldRow">
			                    <td class="fieldLabelsBottomCell fieldCellSize33">
			                        <span data-translationKey="summary_OptProds_SignDate"></span>
			                    </td>
			                    <td id="signDate_id_3" class="fieldLabelsCell fieldValuesBottomCell">
			                        ${activationItems.getFormatedDate(activationItems.getModel('signatureModel').get('signDate'))}
			                    </td>
			                </tr>
		                </tbody>
		                </table> 
					{{/if}}	
			<!--  End Credit Protector Complete case -->
			      <br>
			<!--  Start Not this time  case -->
					{{if activationItems.getModel('OptionalProductsModel').get('optionalProducts_NA')==='Y' }}
					<table id="summary_SelectedProductTable" class="stretchThisTable">
		                <tbody>
			                <tr class="fieldRow">
			                    <td class="fieldLabelsSingleCell fieldCellSize33">
			                        <span data-translationKey="summary_OptProds_ProductSelected"></span>
			                    </td>
			                    <td class="fieldLabelsCell fieldValuesOneCell">
									<span data-translationKey="summary_OptProds_ProdName_NA"></span>
			                    </td>
			                </tr>
		                </tbody>
					{{/if}}
			<!--  End Not this time case -->
	            </table>
			</div>
		{{/if}}
		<br>
		<div>
		<br>

		<center>
			<div class="wrapLoginTable">
	            <table id="loginFieldsTable">
	            	{{if activationItems.getModel('loginScreen').get('employerID').toUpperCase() === 'E'}}
	                <tr class="fieldRow">

	                    <td class="fieldLabelsTopCell fieldSize55">
	                        <span data-translationKey="loginScreen_First_Name"></span>
	                    </td>
	                    <td class="fieldValuesTopCell">
	                        <input class="fieldValuesTextField upperCaseField" id="summaryFirstNameTextField" maxlength="22" type="text" tabindex=1/>
	                    </td>
	                </tr>
	                <tr class="fieldRow">
	                    <td class="fieldLabelsBottomCell  fieldSize55">
	                        <span data-translationKey="loginScreen_Last_Name"></span>
	                    </td>
	                    <td class="fieldValuesBottomCell ">
	                        <input class="fieldValuesTextField upperCaseField" id="summaryLastNameTextField" maxlength="22" type="text" tabindex=2/>
	                    </td>
	                </tr>
	                {{/if}}
	                {{if activationItems.getModel('loginScreen').get('employerID').toUpperCase() !== 'E'}}
					<tr class="fieldRow">
	                    <td class="fieldLabelsSingleCell fieldSize55">
	                        <span data-translationKey="loginScreen_AgentID_Label"></span>
	                    </td>
	                    <td class="fieldValuesOneCell">
	                        <input class="fieldValuesTextField upperCaseField" id="summaryAgentIDTextField" type="text" maxlength="8" tabindex=3/>
	                    </td>
	                </tr>
	                {{/if}}
	            </table>
			</div>
			<br>
			<br>
			<div>
            	<span id="summary_SubmitButton" class="button grayflat applyButtonWidth">
            		<span class="buttonText" data-translationKey="summary_SubmitButton"></span>
				</span>
			</div>	
			<br>		
			<div>
            	<span id="summary_DupeSubmitButton" class="button grayflat applyButtonWidth">
            		<span class="buttonText" data-translationKey="summary_DupeSubmitButton"></span>
				</span>
			</div>
			<br>
			<div>
            	<span id="summary_TestSubmitButton" class="button grayflat applyButtonWidth">
            		<span class="buttonText" data-translationKey="summary_TestSubmitButton"></span>
				</span>
			</div>
			<br>
			<div class="logoIcon logoIconUnclicked"></div>
        </center>
		<br>
		<div>
		<br>

	</div>
</script>

<script id="WICISupCardScreen-template" type="text/x-jquery-tmpl">	
	<div class="ScreenLayout" id="SupCardScreen_PageContents">
	
	    <br><br><br>
	    <center>
	        {{if activationItems.getModel('chooseProductModel').get('productCard') == "OMX" || activationItems.getModel('chooseProductModel').get('productCard') == "OMZ"}}
		           <a href="#" id="suppCard_OMX" class="supplementaryCardImage"></a>
		    {{else activationItems.getModel('chooseProductModel').get('productCard') === "OMP" || 
	                    	               activationItems.getModel('chooseProductModel').get('productCard') === "OMR"}} 
	              <a href="#" id="suppCard_OMP_OMR" class="supplementaryCardImage"></a>   
		    {{/if}} 
	     </center>
	     
		<br>		
		<br>		
		<center>
			<span id="SupCardClickHere" class="pageTitle" data-translationKey="supCardRequest_PageTitle"></span>
		</center>
		<br>
		<br>
		<center>
			<div id="SupCardRequest_TitleAreaYesNO" >
	            <table id="titleTable" class="stretchThisTable" >
	                <tr >
	                    <td class="fieldLabelsSingleCell" colspan="4">
	                        <span data-translationKey="supCardRequest_WouldYouLikeACard"></span>
	                    </td>
	                    <td class="fieldValuesOneCell fieldValuesCellFlip">
	                        <select name="flipSupplementaryCard" id="flipSupplementaryCard" data-role="slider">
								<option id="flipSupplementaryCard_no" value="N">No</option>
								<option id="flipSupplementaryCard_yes" value="Y">Yes</option>
							</select> 
	                    </td>	                    
	                </tr>
	            </table>
			</div>
		</center>
	<br>
	<br>
	<div id="supCardDetailsPanel" >
		<center>
				<div class="supCardAttention" name="supCardAttentionId" id="supCardAttentionId">
					<p class="supCardAttentionText" data-translationKey="supCardAttention_Text"></p>
				</div>
				<br>
				<table class="regularTableEx stretchThisTable tableNoCellsBorder" id="supCardRequestType_Group">
					<tr>
						<td class="heightForRadioButtonLabelCell labelPadingLeft15">
							<span class="fieldLabelsTitle" data-translationKey="supCardRequest_ForWhom"></span>						
						</td>
					</tr>
					<tr>
						<td>
							<center>
								<fieldset  id="supCardRequestType" data-role="controlgroup" data-type="horizontal" data-role="fieldcontain" class="actionButtonGroup ui-controlgroup ui-controlgroup-horizontal">
									<a id="supCardRequest_Spouse_RadioButton" href="#" data-theme="b" data-role="button" class="buttonItem ui-btn ui-corner-left ui-btn-up-c">
										<span class="ui-btn-inner" aria-hidden="true">
											<span class="ui-btn-text" data-translationKey="supCardRequest_Spouse"></span>
										</span>
									</a>
									<a id="supCardRequest_Son_RadioButton" href="#" data-theme="b" data-role="button" class="buttonItem ui-btn ui-btn-up-c">
										<span class="ui-btn-inner" aria-hidden="true">
											<span class="ui-btn-text" data-translationKey="supCardRequest_Son"></span>
										</span>
									</a>
									<a id="supCardRequest_Daughter_RadioButton" href="#" data-theme="b" data-role="button" class="buttonItem ui-btn ui-btn-up-c">
										<span class="ui-btn-inner" aria-hidden="true">
											<span class="ui-btn-text" data-translationKey="supCardRequest_Daughter"></span>
										</span>
									</a>									
									<a id="supCardRequest_Relative_RadioButton" href="#" data-theme="b" data-role="button" class="buttonItem ui-btn ui-btn-up-c">
										<span class="ui-btn-inner" aria-hidden="true">
											<span class="ui-btn-text" data-translationKey="supCardRequest_Relative"></span>
										</span>
									</a>									
									<a id="supCardRequest_Other_RadioButton" href="#" data-theme="b" data-role="button" class="buttonItem ui-btn ui-corner-right ui-controlgroup-last ui-btn-up-c">
										<span class="ui-btn-inner" aria-hidden="true">
											<span class="ui-btn-text" data-translationKey="supCardRequest_Other"></span>
										</span>
									</a>
								</fieldset>
							</center>			
						</td>
					</tr>
				</table>
			</center>
			<br>
			<br>	 
			<center>
				<div id="SupCard_PersonalDetailsArea" >
		            <table class="stretchThisTable">
		                <tr class="fieldRow">
		                    <td class="fieldLabelsTopCell fieldCellSize33">
		                        <span data-translationKey="supCardRequest_FirstName"></span>
		                    </td>
		                    <td class="fieldValuesTopCell">
		                        <input class="fieldValuesTextField upperCaseField" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false" maxlength="30" id="supCardRequest_FirstName_TextField" type="text" tabindex=1/>
		                    </td>
		                </tr>
		                <tr class="fieldRow">
		                    <td class="fieldLabelsCell fieldCellSize33">
		                        <span data-translationKey="supCardRequest_Initial"></span>
		                    </td>
		                    <td class="fieldValuesCell">
		                        <input class="fieldValuesTextField upperCaseField" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false" id="supCardRequest_Initial_TextField" type="text" maxlength="1" tabindex=2 />
		                    </td>
		                </tr>
		                <tr class="fieldRow">
		                    <td class="fieldLabelsCell fieldCellSize33">
		                        <span data-translationKey="supCardRequest_LastName"></span>
		                    </td>
		                    <td class="fieldValuesCell">
		                        <input class="fieldValuesTextField upperCaseField" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false" id="supCardRequest_LastName_TextField" maxlength="40" type="text" tabindex=3/>
		                    </td>
		                </tr>
		                <tr class="fieldRow">
		                    <td class="fieldLabelsCell fieldCellSize33">
		                        <span data-translationKey="supCardRequest_DateOfBirth"></span>
		                    </td>
		                    <td class="fieldValuesCell">
		                        <input class="fieldValuesTextField" id="supCardRequest_DateOfBirth_TextField"  min="1800-01-01" max="3000-12-31" type="date" tabindex=4/>
		                    </td>
		                </tr>
		                <tr class="fieldRow">
		                    <td class="fieldLabelsBottomCell fieldCellSize33">
		                        <span data-translationKey="supCardRequest_Telephone"></span>
								<span id="sup_phone_div">
				          			<a class="tooltip contactInfomation_i_icon margintopbottomleft" id="sup_infomation_phone">
             		     			<span class="tooltipphonetextFinSuppPage" data-translationKey="contactInfo_PhoneToolTipMsg"></span>
                        			</a>
                       			</span>
		                    </td>
		                    <td class="fieldValuesBottomCell">
		                        <input class="fieldValuesPhoneField" id="supCardRequest_Telephone_TextField" type='tel' maxlength="10" tabindex=5/>
		                    </td>
		                </tr>
		            </table>
				</div>
			</center>
			<br>		
			<br>
			<br>
			<center>
		            <table id="titleTable" class="stretchThisTable">
		                <tr >
		                    <td class="fieldLabelsSingleCell" colspan="6">
		                        <span data-translationKey="supCardRequest_SameAddyPrimaryApplicant"></span>
		                    </td>
		                    <td class="fieldValuesOneCell fieldValuesCellFlip">
		                        <select name="flipSameAddress" id="flipSameAddress" data-role="slider">
									<option id="flipSameAddress_no" value="N">No</option>
									<option id="flipSameAddress_yes" value="Y">Yes</span></option>									
								</select> 
		                    </td>
		                </tr>
		            </table>
			</center>
		</div>
		<br>
		<br>
		<br>
		<div id="supAddressPanel" >
		<div class="row">
            <div class="column_two elementWithBoldFont">
               <!-- VZE-265 -->
               <p class="bold fontSize30 currentMailingAddress"><span id="i_icon_sup_card" data-translationKey="personalData_current_Mailing_address"></span>
			 		<span>
						<a class="tooltip personalInfomation_i_icon" id="suppCard_infomation_cityName">
             				<span class="tooltipCityNametext" data-translationKey="personalData_cityNameLongerThen24CharError_msg"></span>
                		</a>
             </span><br> 
            </div>
        </div>
        
        <div class="row">
             <div class="column_one">
                 <div id="canadaPostLogo" class="personalInfo_CanadianPostLogo"></div>
              </div>
              <div class="column_two">
                   <input id="supStreetAddress" placeholder="Start typing a street address or postal code" type="text" class="streetAddressCanadaPost upperCaseField" >
              </div>
        </div>
        <br>
         <div id="sup_canadaPost_SearchedAddress" class="personalInfoColorGray fontSize19 width100 uppercase">
            <div class="elementWithBoldFont">
                <span id="address_supunit" name="address_supunit"></span><span id="address_supunit_hyphen">-</span>
                <span id="address_supLine1" name="address_supLine1"></span><span>, </span><br>
				<span id="address_supLine2" name="address_supLine2"></span></span><span  id="address_supLine2_br"><br></span>
				<span id="address_supCity" name="address_supCity"></span><span>, </span>			
				<span id="address_supProvince" name="address_supProvince"></span><br>
				<span id="address_supPostalcode" name="address_supPostalcode"></span>
            </div>
        </div>
		<br>
		<div id="sup_canadaPostAddressDescription1" class="fontSize20 personalInfoColorGray elementWithTop17 paddingBottom40">
           <span data-translationKey="personalData_canadaPostAddressPara1"></span>
        </div>
		<center>
            <span id="sup_EditAddressButton" class="personalInfo_CanadaPostButton greenColor">
                <span data-translationKey="personalData_EditAddressButton"></span>
			</span>
         </center>  
        <div id="sup_enterAddressManuallySection" class="elementWithTop17 paddingBottom40">
        <div class="row stretchThisTable">
            <div class="column_one tdWidth15">
                <span data-translationKey="personalData_PreviousAddress_SuiteUnit" class="canadaPostInputBoxTitle personalInfoColorGray"></span><br>
                <input id="sup_SuiteUnit_TextField" placeholder="Unit #" autocomplete="off" autocorrect="off" spellcheck="false" type="text" class="canadaPostInput margin_title_text upperCaseField" name="supMulti-unit" >
            </div>
            <div  class="column_one">
                <a class="tooltip" href="#" id="suppCardInfo_infomation_button">
                    <span class="tooltiptext" data-translationKey="personalData_POBoxToolTipMsg"></span>
                </a>
             </div>
            <div class="marginCanadaPostLine1 column_one androidPaytdWidth">
                <span data-translationKey="personalData_PreviousAddress_AddressLine1" class="canadaPostInputBoxTitle personalInfoColorGray"></span><br>
                <input id="sup_AddressLine1_TextField" placeholder="Street Number, Street Name" autocomplete="off" autocorrect="off" spellcheck="false" type="text" class="canadaPostInput margin_title_text upperCaseField" name="supAddressLine1" maxlength="40">
            </div>
            <div id="supAddressLine2Section" class="marginCanadaPostLine1 column_one androidPaytdWidth">
                <span data-translationKey="personalData_PreviousAddress_AddressLine2" class="canadaPostInputBoxTitle personalInfoColorGray"></span><br>
                <input id="sup_AddressLine2_TextField" placeholder="Mailing Address Line 2" autocomplete="off" autocorrect="off" spellcheck="false" type="text" class="canadaPostInput margin_title_text upperCaseField" name="supAddressLine2" >
            </div>
        </div><br>
        <div class="row stretchThisTable">
            <div class="column_one tdWidth35">
                <span data-translationKey="personalData_PreviousAddress_City" class="canadaPostInputBoxTitle personalInfoColorGray"></span><br>
                <input id="sup_City_TextField" placeholder="City" autocomplete="off" autocorrect="off" spellcheck="false" type="text" class="margin_title_text canadaPostInput upperCaseField" name="supCity" >
            </div>
            <div class="marginCanadaPostLine2 inputTextAlignment">
                <span data-translationKey="personalData_PreviousAddress_Province" class="canadaPostInputBoxTitle personalInfoColorGray"></span><br>               
                <select id="sup_Province_TextField" placeholder="Province" type="text" class="canadaPostProvinceHeight margin_title_text upperCaseField" name="supProvinceCode" ></select>
            </div>
            <div class="column_one tdWidth20">
                <span data-translationKey="personalData_PreviousAddress_PostalCode" class="canadaPostInputBoxTitle personalInfoColorGray"></span><br>
                <input id="sup_PostalCode" placeholder="L#L#L#" autocomplete="off" autocorrect="off" spellcheck="false" type="text" class="canadaPostInput form-control margin_title_text upperCaseField" name="supPostalcode" >
            </div>
        </div>
        </div>
		<center>
            <span id="sup_AcceptButton" class="personalInfo_CanadaPostButton greenColor">
                <span  data-translationKey="personalData_AcceptButton"></span>
			</span>
         </center>
        <p id="sup_canadaPost_addressSearch_instructions" class="fontSize16 personalInfoColorGray">
            <span data-translationKey="personalData_canadaPostAddress_1"></span><br>
            <span data-translationKey="personalData_canadaPostAddress_2"></span><br><br>
            <span data-translationKey="personalData_canadaPostAddress_3"></span><br><br>
            <span data-translationKey="personalData_canadaPostAddress_4"></span>
         </p>
         <br>
         <center>
            <span id="sup_EnterAddressManuallyButton" class="personalInfo_CanadaPostButton greenColor">
                <span  data-translationKey="personalData_EnterAdressManuallyButton"></span>
			</span>
         </center>
		 <br>

		</div>
		<br>
		<br>
		<br>
	</div>	
</script>		
<script id="AttestTrainingScreen-template" type="text/x-jquery-tmpl">
    <div class="ScreenLayout" id="AttestTrainingScreen_PageContents">    		
        <center>
             <center>
                <div class="wrapLoginTable">
                    <table id="trainingModuleTable" class="loginTableWidth">
                        <tr class="fieldRow">
                            <td class="fieldLabelsTopCell fieldSize55">
                                <span data-translationKey="loginScreen_Location_Number"></span>
                            </td>
                            <td class="fieldValuesCell">
                                <input class="fieldValuesTextField upperCaseField" id="locationNumberTextField2" type="text" maxlength="5" tabindex=2/>
                            </td>
                        </tr>
                        <tr class="fieldRow ">
                            <td class="fieldLabelsCell fieldSize55">
                                <span data-translationKey="loginScreen_First_Name"></span>
                            </td>
                            <td class="fieldValuesCell">
                                <input class="fieldValuesTextField upperCaseField" id="firstNameTextField2" maxlength="30" type="text" tabindex=3/>
                            </td>
                        </tr>
                        <tr class="fieldRow">
                            <td class="fieldLabelsBottomCell fieldSize55">
                                <span data-translationKey="loginScreen_Last_Name"></span>
                            </td>
                            <td class="fieldValuesCell">
                                <input class="fieldValuesTextField upperCaseField" id="lastNameTextField2" maxlength="30" type="text" tabindex=4/>
                            </td>
                        </tr>
                         <tr class="fieldRow hidden" id="employeeNumberID_Row">
                            <td class="fieldLabelsBottomCell fieldSize55">
                                <span data-translationKey="loginScreen_EmployeeNumberID_Label"></span>
                            </td>
                            <td class="fieldValuesCell">
                                <input class="fieldValuesTextField" id="employeeNumberIDTextField2" type="tel" maxlength="9" tabindex=2/>
                            </td>
                        </tr>
                    </table>
                </div>
                <br>
            </center>
                <br>
                <center>
                    <div id="attest_training_completion">
                            <br>
                            <center>
                                <div class="signatureTitle">
                                    <span class="signatureTitleFont" data-translationKey="loginScreen_signatureBox_title"></span>
                                </div>
                            </center>
                            <br>
                            <div class="signatureContainer sugnatureFixesLogine">
                                <div class="signatureBorder" id="attestScreen_SignatureContainer">
                                    <div class="signature" id="signatureOfTrainee"/></div>
                                </div>
                                <br>
                                <span id="signature_Reset_Button_attest_Screen" class="button grayflat sigResetButton proceedButtonWidth"> 
                                    <span class="buttonText" data-translationKey="signatureScreen_Reset_Button_Label"></span>
                                </span>
                            </div>
                        </center>
                <br>
                    <center>
			               <div id="OtherStaff_TitleAreaYesNO" class="wrapLoginTable">
	                           <table id="titleTable" class="stretchThisTable" >
	                             <tr >
	                                 <td class="fieldLabelsSingleCellYesNo">
	                                      <span data-translationKey="attestscreen_toggleTitle"></span>
	                                 </td>                    
	                            </tr>
	                          </table>
			              </div>
			              <br>
                    </center>
                 <center>
                    <table id="employeeEmailArea" class="wrapLoginTable">
                        <tr class="fieldRow ">
                            <td class="fieldLabelsCell fieldSize55" style="border-radius: 11px 0 0 11px;">
                                <span data-translationKey="employee_email"></span>
                            </td>
                            <td class="fieldValuesCell">
                                <input class="fieldValuesTextField" id="EmailAddress_TextField" type='email' maxlength="60" tabindex=3/>
                            </td>
                        </tr>
                    </table>
                 </center>

               <br>


         <span id="attestTrainingScreen_next" class="button greenflat loginButtonWidth">
               <span class="buttonText" data-translationKey="traningScreen_attest_button_label"></span>
         </span>
         <br>
        </center>  			
	</div>		
</script>
<script id="TrainingModuleScreens-template" type="text/x-jquery-tmpl">
    <div class="ScreenLayout" id="trainingModuleScreens_PageContents">
     <center>
        <span>
            <img id="pdf" class="trainingContentImages"> </img> 
		</span>
  		<br>
		<br>
         <span id="trainingScreen2_next" class="button greenflat loginButtonWidth">
            <span class="buttonText" data-translationKey="finishTraining_Button_label"></span>
         </span>
    </center>  			
	</div>
</script>
<script id="TrainingModuleStartScreen-template" type="text/x-jquery-tmpl">
    <div class="ScreenLayout" id="trainingModuleStartScreen_PageContents" >
        <br>
        <br>
        <!-- <div class="flipAlignRight">
        <select id="language_choser" data-role="slider" >
            <option value="E" selected>En</option>
            <option value="F">Fr</option>
        </select>
        <br>
        <br>
        </div> 
        //-->
        <center>
            <span class="training_title" data-translationKey="startTrainingModuleScreenTitle"></span>
        </center>	
        <br>
        <br>
        <br>
        <br>
        <div id="startTrainingScreen_FieldsArea">
            <center>
                <div class="wrapLoginTable">
                    <table id="startTrainingFieldsTable" class="loginTableWidth">
                        <tr class="fieldRow">
                            <td class="fieldLabelsTopCell fieldSize55">
                                <span data-translationKey="loginScreen_Location_Number"></span>
                            </td>
                            <td class="fieldValuesTopCell">
                                <input class="fieldValuesTextField upperCaseField" id="locationNumberTextField1" type="text" maxlength="5" tabindex=2/>
                            </td>
                        </tr>
                        <tr class="fieldRow ">
                            <td class="fieldLabelsCell fieldSize55">
                                <span data-translationKey="loginScreen_First_Name"></span>
                            </td>
                            <td class="fieldValuesCell">
                                <input class="fieldValuesTextField upperCaseField" id="firstNameTextField1" maxlength="30"  type="text" tabindex=3/>
                            </td>
                        </tr>
                        <tr class="fieldRow">
                            <td class="fieldLabelsBottomCell fieldSize55">
                                <span data-translationKey="loginScreen_Last_Name"></span>
                            </td>
                            <td class="fieldValuesCell">
                                <input class="fieldValuesTextField upperCaseField" id="lastNameTextField1" maxlength="30" type="text" tabindex=4/>
                            </td>
                        </tr>
                            <tr class="fieldRow hidden">
                            <td class="fieldLabelsBottomCell fieldSize55">
                                <span data-translationKey="loginScreen_EmployeeNumberID_Label"></span>
                            </td>
                            <td class="fieldValuesCell">
                                <input class="fieldValuesTextField" id="employeeNumberIDTextField1" type="tel" maxlength="9" tabindex=2/>
                            </td>
                        </tr>
                    </table>
                </div>
            </center>
        </div>
        <br>
        <br>
        <br>
        <center>
            <span id="startTrainingScreen_startTraining" class="button greenflat loginButtonWidth">
                <span class="buttonText" data-translationKey="startTraining_Button_label"></span>
            </span>
            <br>
        </center>  			
    </div>
</script>
        <title>WICIWebApp</title>
    </head>
    <body topmargin="0" leftmargin="0">
        <div data-role="page" id="WICIWebAppMainPage">

        	<!-- Loading Icon -->
			<div id="loadingIconBackgroundPane" style="display:none">
				<div id="loadingIconPane">
		    		<h2>
		    			<span data-translationKey="app_loading">Loading...</span>
		    		</h2>
			       	<span id="loadingIcon"></span>
		       	</div>
			</div>
            
            <!-- Login Screen -->
            <section id="WICILoginScreen" class="wiciLoginPage" style="display:none">			
            </section>
            
            <!-- Credit Card Selection Screen -->
            <section id="ChooseProductScreen" class="wiciWhiteBackgroundPage" style="display:none">
            </section>			
						
			<!-- Personal Data Screen -->
            <section id="PersonalDataScreen" class="wiciWhiteBackgroundPage" style="display:none">
            </section>

			<!-- Personal Data Screen 2 -->
            <section id="PersonalDataScreen2" class="wiciWhiteBackgroundPage" style="display:none">
            </section>
            
            <!-- US4637 -->
            <!-- Contact Information -->
            <section id="ContactInfoScreen" class="wiciWhiteBackgroundPage" style="display:none">
            </section>

            <!-- Financial And Employment Screen -->
            <section id="FinancialScreen" class="wiciWhiteBackgroundPage" style="display:none">
            </section>

            <!-- Supplementary Card Request Screen -->
            <section id="SupplementaryCardRequestScreen" class="wiciWhiteBackgroundPage" style="display:none">
            </section>

            <!-- Signature Screen -->
            <section id="SignatureScreen" class="wiciWhiteBackgroundPage" style="display:none">
            </section>

            <!-- Optional Products Screen -->
            <section id="OptionalProductsScreen" class="wiciWhiteBackgroundPage" style="display:none">
            </section>
            
            <!-- US4926 -->
            <!-- Mobile Payments -->
            <section id="MobilePaymentsScreen" class="wiciWhiteBackgroundPage" style="display:none">
            </section>
			           
            <!-- Summary Screen -->
            <section id="SummaryScreen" class="wiciWhiteBackgroundPage" style="display:none">
            </section>
            
            <!-- Print Screen -->
            <section id="PrintDemoScreen" class="wiciWhiteBackgroundPage" style="display:none">
            </section>

            <!-- Confirmation Screen -->
            <section id="ConfirmationScreen" class="wiciWhiteBackgroundPage" style="display:none">
            </section>

            <!-- Status Screen -->
            <section id="StatusScreen" class="wiciWhiteBackgroundPage" style="display:none">
            </section>

            <!-- Pending Screen -->
            <section id="PendingScreen" class="wiciWhiteBackgroundPage" style="display:none">
            </section>
            
            <!-- AgentAttribution Screen -->
            <section id="AgentAttributionScreen" class="wiciWhiteBackgroundPage" style="display:none">
            </section>
            
            <!-- InstantIssuance Setup Instructions Screen -->
            <section id="InstantIssuanceSetupInsScreen" class="wiciWhiteBackgroundPage" style="display:none">
            </section>
            
            <!-- InstantIssuance Setup Complete Screen -->
            <section id="InstantIssuanceSetupCompleteScreen" class="wiciWhiteBackgroundPage" style="display:none">
            </section>

             <!-- Training module start screen -->
             <section id="TrainingModuleStartScreen" class="wiciWhiteBackgroundPage" style="display:none">
            </section>

             <!--  Training module screen 2-->
            <section id="TrainingModuleScreens" class="wiciWhiteBackgroundPage" style="display:none">
            </section>

            <!-- Training AttestTrainningScreen -->
            <section id="AttestTrainingScreen" class="wiciWhiteBackgroundPage" style="display:none">
            </section>            

			<script>
				/**
				 * Determine whether the file loaded from PhoneGap or not
				 */
				function isPhoneGap() {
				    return (cordova || PhoneGap || phonegap) 
				    && /^file:\/{3}[^\/]/i.test(window.location.href) 
				    && /ios|iphone|ipod|ipad|android/i.test(navigator.userAgent);
				}
	
				if ( isPhoneGap() ) {
				    document.addEventListener("deviceready", onDeviceReady, false); //Running on PhoneGap
				} else {
				    onDeviceReady(); //Running NOT on PhoneGap
				}
			
				function onDeviceReady() {
		        	app = new WICI.WICIWebApp();
                	app.init();
		    	}
				
             	$(document).ready(function(){                	 
    	            document.addEventListener("deviceready", onDeviceReady, false);
        	     });
			</script>
        </div>
    </body>
</html>
