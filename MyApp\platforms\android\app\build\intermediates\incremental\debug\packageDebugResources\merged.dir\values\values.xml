<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:ns1="http://schemas.android.com/tools">
    <color name="cdv_splashscreen_background">#FFFFFF</color>
    <string name="activity_name">@string/launcher_name</string>
    <string name="app_name">MyApp</string>
    <string name="launcher_name">@string/app_name</string>
    <string name="runtime_permission_error_message1">This app requires certain permissions to function properly. Please grant the requested permissions.</string>
    <string name="runtime_permission_error_ok_button">OK</string>
    <string name="runtime_permission_error_title">Permission Required</string>
    <style name="Theme.App.SplashScreen" parent="Theme.SplashScreen.IconBackground">
        <item name="windowSplashScreenBackground">@color/cdv_splashscreen_background</item>
        <item name="windowSplashScreenAnimatedIcon">@drawable/ic_cdv_splashscreen</item>
        <item name="windowSplashScreenAnimationDuration">200</item>
        <item name="postSplashScreenTheme">@style/Theme.AppCompat.NoActionBar</item>
        <item name="android:windowOptOutEdgeToEdgeEnforcement" ns1:targetApi="35">true</item>
    </style>
</resources>