package com.ctfs.wicimobile.plugins;

import org.apache.cordova.CallbackContext;
import org.apache.cordova.CordovaPlugin;
import org.apache.cordova.PluginResult;
import org.json.JSONArray;
import org.json.JSONException;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.util.Collections;
import java.util.List;
import java.util.ArrayList;

import com.ctfs.wicimobile.util.WICIDeviceInfoHelper;

import android.annotation.TargetApi;
import android.app.Activity;
import android.os.Build;
import android.util.Log;

import android.net.ConnectivityManager;
import android.net.Network;
import android.net.NetworkCapabilities;
import android.content.Context;

@TargetApi(Build.VERSION_CODES.GINGERBREAD)
public class DeviceInfoPlugin extends CordovaPlugin
{
	private static final String TAG = "DeviceInfoPlugin";
	private static final String NOT_SUPPORTED_ACTION = "Not supported action: ";
	private final int ipV6Truncated = 15;
	
	private final String spoofedMFGSerial = ""; 
	private final String spoofedBuildSerial = "";
	
    public enum ConnType {
        WIFI,
        LTE,
        BOTH,
        NONE
    }	
	
	public Activity getCurrentContext()
	{
		return this.cordova.getActivity();
	}

	private String getManufacturerSerialNumber()
	{
		String mfgSerial = ("".equals(spoofedBuildSerial)) ? WICIDeviceInfoHelper.getInstace(this.cordova.getActivity()).getDeviceSerialNo() : spoofedMFGSerial;
		return mfgSerial;
	}

	private String getBuildSerialNumber()
	{		
		String buildSerial = ("".equals(spoofedBuildSerial)) ? Build.SERIAL : spoofedBuildSerial; 
		Log.i(TAG, "getBuildSerialNumber: " + buildSerial );
		printAndroidVersions();
		return buildSerial;
	}
	
	private void printAndroidVersions() {
        // Get Android version (e.g., "10" or "11")
        String androidVersion = Build.VERSION.RELEASE;

        // Get API level (e.g., 29 for Android 10, 30 for Android 11)
        int sdkVersion = Build.VERSION.SDK_INT;

        Log.i(TAG, "Android Version: " + androidVersion);
        Log.i(TAG, "SDK Version: " + sdkVersion);
	}


    public ConnType getNetworkType(Context argContext) {
        ConnectivityManager connectivityManager = (ConnectivityManager) argContext.getSystemService(Context.CONNECTIVITY_SERVICE);

        if (connectivityManager != null) {
            Network network = connectivityManager.getActiveNetwork();
            if (network != null) {
                NetworkCapabilities capabilities = connectivityManager.getNetworkCapabilities(network);
                if (capabilities != null) {
                    boolean hasWifi = capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI);
                    boolean hasCellular = capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR);

                    if (hasWifi && hasCellular) {
                        return ConnType.BOTH;
                    } else if (hasWifi) {
                        return ConnType.WIFI;
                    } else if (hasCellular) {
                        return ConnType.LTE;
                    }
                }
            }
        }
        return ConnType.NONE;
    }	
	
	private boolean isIPv4( String argIP ) {
		return argIP.indexOf(':')<0;
	}
	
	private String getIPAddress(boolean argUseIP4) {
		Log.i(TAG, "getIPAddress" );
		printAndroidVersions();
		String tabletIP = "";
		String currInterface = "";
		ArrayList<String> networkIfacesAndIPAddresses = new ArrayList<String>();
		ConnType network = null;
		//LTE Network Interfaces
		//rmnet0		(LTE)
		//rmnet_data0	(LTE)
		//rmnet_data1	(LTE)
		//ccmni0		(LTE)
		//qmi0			(LTE)
		//wwan0			(LTE)
        try {
        	
        	network = getNetworkType(getCurrentContext());
        	Log.i(TAG, "--getIPAddress: network=" + network.toString() );
        	
        	if( network.equals(ConnType.NONE) )
        		return tabletIP;
        	
            List<NetworkInterface> interfaces = Collections.list(NetworkInterface.getNetworkInterfaces());
            for (NetworkInterface intf : interfaces) {
                List<InetAddress> addrs = Collections.list(intf.getInetAddresses());
                Log.i(TAG, "--getIPAddress: interface name=" + intf.getName() );
                currInterface = intf.getName().toUpperCase();
                Log.i(TAG, "--getIPAddress: currInterface=" + currInterface );
                for (InetAddress addr : addrs) {
                    if (!addr.isLoopbackAddress()) {
                        String sAddr = addr.getHostAddress();
                        Log.i(TAG, "----getIPAddress: sAddr=" + sAddr );
                        boolean isIPv4 = sAddr.indexOf(':')<0;

                        if (!isIPv4) {
                        	int delim = sAddr.indexOf('%'); // drop ip6 zone suffix
                        	sAddr = delim<0 ? sAddr.toUpperCase() : sAddr.substring(0, delim).toUpperCase();
                        	Log.i(TAG, "----getIPAddress: IPv6 cleanse: sAddr=" + sAddr );
                        }                        

                        if( currInterface.length()>=3 ) {
                        	if( currInterface.substring(0,3).equals("QMI")) {
                        		Log.i(TAG, "-----getIPAddress: adding " + intf.getName()+"="+sAddr);
                        		networkIfacesAndIPAddresses.add(intf.getName()+"="+sAddr);
                        	}
                        	//Reject the Tunnel IP (since its always the same i.e. **************
							//if( currInterface.startsWith("TUN") )  {
							//	Log.i(TAG, "-----getIPAddress: adding " + intf.getName()+"="+sAddr);
							//	networkIfacesAndIPAddresses.add(intf.getName()+"="+sAddr);	
							//}    
                            if( currInterface.length()>=4 )  {
                            	if(currInterface.startsWith("WWAN")) {
                                	Log.i(TAG, "-----getIPAddress: adding " + intf.getName()+"="+sAddr);
                                	networkIfacesAndIPAddresses.add(intf.getName()+"="+sAddr);	
                            	}
                            	if( currInterface.startsWith("WLAN") && isIPv4 ){
                                	Log.i(TAG, "-----getIPAddress: adding " + intf.getName()+"="+sAddr);
                                	if(network.equals(ConnType.WIFI))
                                		networkIfacesAndIPAddresses.add(intf.getName()+"="+sAddr);
                                }     
                            	if( currInterface.length()>=5 && !currInterface.startsWith("DUMMY") ){
                            		if( currInterface.startsWith("RMNET") && !isIPv4 ) {                            		
                                    	if(network.equals(ConnType.LTE)) {
                                    		Log.i(TAG, "-----getIPAddress: adding " + intf.getName()+"="+sAddr);
                                    		networkIfacesAndIPAddresses.add(intf.getName()+"="+sAddr);
                                    	}
                                    }
                            		if( currInterface.startsWith("RMNET") && isIPv4){
                                    	if(network.equals(ConnType.LTE)) {
                                    		Log.i(TAG, "-----getIPAddress: adding " + intf.getName()+"="+sAddr);
                                    		networkIfacesAndIPAddresses.add(intf.getName()+"="+sAddr);
                                    	}
                                    }
                            		if( currInterface.startsWith("CCMNI") ) {
                                    	Log.i(TAG, "-----getIPAddress: adding " + intf.getName()+"="+sAddr);
                                    	networkIfacesAndIPAddresses.add(intf.getName()+"="+sAddr);	
                                    }
                                }                                 
                            }                             
                        }                        
                    }
                }
            }
        } catch (Exception e){ 
        	e.printStackTrace();
        } 		
        
//        int index = 0;
//        int interfacesLength = networkIfacesAndIPAddresses.size();
//        for( String thisIP : networkIfacesAndIPAddresses ) {
//        	tabletIP += thisIP+( (index<interfacesLength-1) ? "|" : "");
//        	Log.i(TAG, "getIPAddress: building result tabletIP=" + tabletIP);
//        	index++;
//        }
        Log.i(TAG, "getIPAddress: final before tabletIP=" + tabletIP);  
        
        if(network.equals(ConnType.LTE)) {
        	tabletIP = findBestIP(networkIfacesAndIPAddresses, network, ConnType.LTE, "RMNET", argUseIP4);
        }
        if(network.equals(ConnType.WIFI)) {        	
        	tabletIP = findBestIP(networkIfacesAndIPAddresses, network, ConnType.WIFI, "WLAN", argUseIP4);
        }
        
        Log.i(TAG, "getIPAddress: final after tabletIP=" + tabletIP);        
        
        return tabletIP;
	}

	private String findBestIP(ArrayList<String> argNetworkIfacesAndIPAddresses, ConnType argThisNetwork, ConnType argFindConnType, String argNetPrefix, boolean argPreferIPv4) {
		String bestIP = "";
    	for( String thisNetwork : argNetworkIfacesAndIPAddresses ) {
    		String thisName = thisNetwork.split("=")[0];
    		String thisIP = thisNetwork.split("=")[1];
    		Log.i(TAG, "getIPAddress: thisName=" + thisName);
    		Log.i(TAG, "getIPAddress: thisIP=" + thisIP);
    		
    		if( argThisNetwork.equals(argFindConnType) ) {
    			if(thisName.toUpperCase().startsWith(argNetPrefix.toUpperCase()) ) {
    				bestIP = thisIP;
    				if( argPreferIPv4 && isIPv4(thisIP) ) {
    					bestIP = thisIP;
        				return thisIP; //If IPV4 is found, return it right away, we prefer this format
    				}
    			}    			
    		}    		
    	}
    	if(!isIPv4(bestIP))
    		bestIP=bestIP.substring(0,ipV6Truncated); //If IPV6 only, return and truncate to 15 chars
    	return bestIP;
	}
	
	private String getDeviceInfo()
	{
		String deviceInfoString = "{}";
		String mfgSerial = getManufacturerSerialNumber();
		String buildSerial = getBuildSerialNumber();
		String ipAddress = getIPAddress(true);

		deviceInfoString = "{ MfgSerial : \"" + mfgSerial + "\", BuildSerial : \"" + buildSerial + "\", IPAddress : \"" + ipAddress + "\" }";

		return deviceInfoString;
	}

	@Override
	public boolean execute(String action, JSONArray args, CallbackContext callbackContext) throws JSONException
	{
		Log.i(TAG,"execute");
		if (action.equals("getDeviceInfo"))
		{
			try
			{
				String deviceInfo = getDeviceInfo();
				PluginResult result = new PluginResult(PluginResult.Status.OK, deviceInfo);
				callbackContext.sendPluginResult(result);
				return true;
			}
			catch (Exception ex)
			{
				Log.e(TAG, action + " execute exception", ex);

				if (callbackContext != null)
				{
					callbackContext.sendPluginResult(new PluginResult(PluginResult.Status.ERROR, ex.getMessage()));
				}
			}
		}
		else
		{
			Log.e(TAG, action + " action is not supported", null);

			if (callbackContext != null)
			{
				callbackContext.sendPluginResult(new PluginResult(PluginResult.Status.ERROR, NOT_SUPPORTED_ACTION + action));
			}
		}

		return false;
	}
}

