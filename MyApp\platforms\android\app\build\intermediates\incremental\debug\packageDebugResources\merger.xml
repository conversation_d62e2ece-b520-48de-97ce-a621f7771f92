<?xml version="1.0" encoding="utf-8"?>
<merger version="3" xmlns:ns1="http://schemas.android.com/tools"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\res"><file name="ic_cdv_splashscreen" path="C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\res\drawable\ic_cdv_splashscreen.xml" qualifiers="" type="drawable"/><file name="ic_launcher" path="C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\res\mipmap-hdpi\ic_launcher.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\res\mipmap-hdpi-v26\ic_launcher.xml" qualifiers="hdpi-v26" type="mipmap"/><file name="ic_launcher_background" path="C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\res\mipmap-hdpi-v26\ic_launcher_background.png" qualifiers="hdpi-v26" type="mipmap"/><file name="ic_launcher_foreground" path="C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\res\mipmap-hdpi-v26\ic_launcher_foreground.png" qualifiers="hdpi-v26" type="mipmap"/><file name="ic_launcher_monochrome" path="C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\res\mipmap-hdpi-v26\ic_launcher_monochrome.png" qualifiers="hdpi-v26" type="mipmap"/><file name="ic_launcher" path="C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\res\mipmap-ldpi\ic_launcher.png" qualifiers="ldpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\res\mipmap-ldpi-v26\ic_launcher.xml" qualifiers="ldpi-v26" type="mipmap"/><file name="ic_launcher_background" path="C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\res\mipmap-ldpi-v26\ic_launcher_background.png" qualifiers="ldpi-v26" type="mipmap"/><file name="ic_launcher_foreground" path="C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\res\mipmap-ldpi-v26\ic_launcher_foreground.png" qualifiers="ldpi-v26" type="mipmap"/><file name="ic_launcher" path="C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\res\mipmap-mdpi\ic_launcher.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\res\mipmap-mdpi-v26\ic_launcher.xml" qualifiers="mdpi-v26" type="mipmap"/><file name="ic_launcher_background" path="C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\res\mipmap-mdpi-v26\ic_launcher_background.png" qualifiers="mdpi-v26" type="mipmap"/><file name="ic_launcher_foreground" path="C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\res\mipmap-mdpi-v26\ic_launcher_foreground.png" qualifiers="mdpi-v26" type="mipmap"/><file name="ic_launcher_monochrome" path="C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\res\mipmap-mdpi-v26\ic_launcher_monochrome.png" qualifiers="mdpi-v26" type="mipmap"/><file name="ic_launcher" path="C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\res\mipmap-xhdpi\ic_launcher.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\res\mipmap-xhdpi-v26\ic_launcher.xml" qualifiers="xhdpi-v26" type="mipmap"/><file name="ic_launcher_background" path="C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\res\mipmap-xhdpi-v26\ic_launcher_background.png" qualifiers="xhdpi-v26" type="mipmap"/><file name="ic_launcher_foreground" path="C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\res\mipmap-xhdpi-v26\ic_launcher_foreground.png" qualifiers="xhdpi-v26" type="mipmap"/><file name="ic_launcher_monochrome" path="C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\res\mipmap-xhdpi-v26\ic_launcher_monochrome.png" qualifiers="xhdpi-v26" type="mipmap"/><file name="ic_launcher" path="C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\res\mipmap-xxhdpi\ic_launcher.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\res\mipmap-xxhdpi-v26\ic_launcher.xml" qualifiers="xxhdpi-v26" type="mipmap"/><file name="ic_launcher_background" path="C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\res\mipmap-xxhdpi-v26\ic_launcher_background.png" qualifiers="xxhdpi-v26" type="mipmap"/><file name="ic_launcher_foreground" path="C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\res\mipmap-xxhdpi-v26\ic_launcher_foreground.png" qualifiers="xxhdpi-v26" type="mipmap"/><file name="ic_launcher_monochrome" path="C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\res\mipmap-xxhdpi-v26\ic_launcher_monochrome.png" qualifiers="xxhdpi-v26" type="mipmap"/><file name="ic_launcher" path="C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\res\mipmap-xxxhdpi\ic_launcher.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\res\mipmap-xxxhdpi-v26\ic_launcher.xml" qualifiers="xxxhdpi-v26" type="mipmap"/><file name="ic_launcher_background" path="C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\res\mipmap-xxxhdpi-v26\ic_launcher_background.png" qualifiers="xxxhdpi-v26" type="mipmap"/><file name="ic_launcher_foreground" path="C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\res\mipmap-xxxhdpi-v26\ic_launcher_foreground.png" qualifiers="xxxhdpi-v26" type="mipmap"/><file name="ic_launcher_monochrome" path="C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\res\mipmap-xxxhdpi-v26\ic_launcher_monochrome.png" qualifiers="xxxhdpi-v26" type="mipmap"/><file path="C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\res\values\colors.xml" qualifiers=""><color name="cdv_splashscreen_background">#FFFFFF</color></file><file path="C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">MyApp</string><string name="launcher_name">@string/app_name</string><string name="activity_name">@string/launcher_name</string><string name="runtime_permission_error_title">Permission Required</string><string name="runtime_permission_error_message1">This app requires certain permissions to function properly. Please grant the requested permissions.</string><string name="runtime_permission_error_ok_button">OK</string></file><file path="C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.App.SplashScreen" parent="Theme.SplashScreen.IconBackground">
        <item name="windowSplashScreenBackground">@color/cdv_splashscreen_background</item>
        <item name="windowSplashScreenAnimatedIcon">@drawable/ic_cdv_splashscreen</item>
        <item name="windowSplashScreenAnimationDuration">200</item>
        <item name="postSplashScreenTheme">@style/Theme.AppCompat.NoActionBar</item>
        <item name="android:windowOptOutEdgeToEdgeEnforcement" ns1:targetApi="35">true</item>
    </style></file><file name="cdv_core_file_provider_paths" path="C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\res\xml\cdv_core_file_provider_paths.xml" qualifiers="" type="xml"/><file name="config" path="C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\res\xml\config.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>