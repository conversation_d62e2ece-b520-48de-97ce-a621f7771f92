#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

module.exports = function(context) {
    console.log('Running copy_native_code hook...');
    
    // Only run for Android platform
    if (context.opts.platforms.indexOf('android') === -1) {
        console.log('Skipping copy_native_code hook - not building for Android');
        return;
    }

    const projectRoot = context.opts.projectRoot;
    const sourceDir = path.join(projectRoot, 'custom_native_code');
    const targetDir = path.join(projectRoot, 'platforms', 'android', 'app', 'src', 'main', 'java');
    const javaLibsDir = path.join(projectRoot, 'javalibs');
    const androidLibsDir = path.join(projectRoot, 'platforms', 'android', 'app', 'libs');

    if (!fs.existsSync(sourceDir)) {
        console.log('Source directory does not exist:', sourceDir);
        return;
    }

    if (!fs.existsSync(targetDir)) {
        console.log('Target directory does not exist:', targetDir);
        return;
    }

    // Function to recursively copy files
    function copyRecursive(src, dest) {
        const stats = fs.statSync(src);
        
        if (stats.isDirectory()) {
            // Create directory if it doesn't exist
            if (!fs.existsSync(dest)) {
                fs.mkdirSync(dest, { recursive: true });
                console.log('Created directory:', dest);
            }
            
            // Copy all files in directory
            const files = fs.readdirSync(src);
            files.forEach(file => {
                const srcFile = path.join(src, file);
                const destFile = path.join(dest, file);
                copyRecursive(srcFile, destFile);
            });
        } else {
            // Copy file
            fs.copyFileSync(src, dest);
            console.log('Copied file:', dest);
        }
    }

    try {
        // Copy all Java files from custom_native_code (excluding the android subfolder)
        const items = fs.readdirSync(sourceDir);
        
        items.forEach(item => {
            const srcPath = path.join(sourceDir, item);
            const stats = fs.statSync(srcPath);
            
            if (stats.isDirectory()) {
                if (item === 'android') {
                    // Special handling for android folder - copy its contents to the target
                    const androidSrcPath = path.join(sourceDir, 'android');
                    if (fs.existsSync(androidSrcPath)) {
                        copyRecursive(androidSrcPath, targetDir);
                    }
                } else {
                    // Copy other directories to com/ctfs/wicimobile/
                    const destPath = path.join(targetDir, 'com', 'ctfs', 'wicimobile', item);
                    copyRecursive(srcPath, destPath);
                }
            } else if (item.endsWith('.java')) {
                // Copy individual Java files to com/ctfs/wicimobile/
                const destPath = path.join(targetDir, 'com', 'ctfs', 'wicimobile', item);
                fs.copyFileSync(srcPath, destPath);
                console.log('Copied file:', destPath);
            }
        });

        console.log('Successfully copied custom native code to Android platform');

        // Copy JAR files from javalibs to Android libs directory
        if (fs.existsSync(javaLibsDir)) {
            // Clean and create libs directory
            if (fs.existsSync(androidLibsDir)) {
                // Remove all existing JAR files
                const existingJars = fs.readdirSync(androidLibsDir).filter(file => file.endsWith('.jar'));
                existingJars.forEach(jar => {
                    const jarPath = path.join(androidLibsDir, jar);
                    fs.unlinkSync(jarPath);
                    console.log('Removed old JAR file:', jarPath);
                });
            } else {
                fs.mkdirSync(androidLibsDir, { recursive: true });
                console.log('Created libs directory:', androidLibsDir);
            }

            // Only copy essential JAR files that are known to work with AndroidX
            const allowedJars = [
                'cmbsdklib-release.jar',  // Cognex Mobile Barcode SDK
                'ZSDK_ANDROID_API.jar',   // Zebra SDK
                'gson-2.1.jar',           // Google JSON library
                'commons-io-2.2.jar',     // Apache Commons IO
                'commons-net-3.1.jar',    // Apache Commons Net
                'opencsv-2.2.jar',        // OpenCSV
                'snmp6_1.jar'             // SNMP library
            ];

            const jarFiles = fs.readdirSync(javaLibsDir)
                .filter(file => file.endsWith('.jar'))
                .filter(file => allowedJars.includes(file));

            jarFiles.forEach(jarFile => {
                const srcJar = path.join(javaLibsDir, jarFile);
                const destJar = path.join(androidLibsDir, jarFile);
                fs.copyFileSync(srcJar, destJar);
                console.log('Copied JAR file:', destJar);
            });

            if (jarFiles.length > 0) {
                console.log(`Successfully copied ${jarFiles.length} JAR files to Android libs directory`);
            }

            // List any JAR files that were skipped
            const allJars = fs.readdirSync(javaLibsDir).filter(file => file.endsWith('.jar'));
            const skippedJars = allJars.filter(jar => !allowedJars.includes(jar));
            if (skippedJars.length > 0) {
                console.log('Skipped potentially problematic JAR files:', skippedJars);
            }
        } else {
            console.log('javalibs directory does not exist, skipping JAR copy');
        }

        // Add missing string resources
        const stringsXmlPath = path.join(projectRoot, 'platforms', 'android', 'app', 'src', 'main', 'res', 'values', 'strings.xml');
        if (fs.existsSync(stringsXmlPath)) {
            let stringsContent = fs.readFileSync(stringsXmlPath, 'utf8');

            // Check if our custom strings are already present
            if (!stringsContent.includes('runtime_permission_error_title')) {
                // Add our custom strings before the closing </resources> tag
                const customStrings = `
    <!-- Runtime permission error strings -->
    <string name="runtime_permission_error_title">Permission Required</string>
    <string name="runtime_permission_error_message1">This app requires certain permissions to function properly. Please grant the requested permissions.</string>
    <string name="runtime_permission_error_ok_button">OK</string>
</resources>`;

                stringsContent = stringsContent.replace('</resources>', customStrings);
                fs.writeFileSync(stringsXmlPath, stringsContent);
                console.log('Added custom string resources to strings.xml');
            }
        }

    } catch (error) {
        console.error('Error copying custom native code:', error);
        throw error;
    }
};
