#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

module.exports = function(context) {
    console.log('Running copy_native_code hook...');
    
    // Only run for Android platform
    if (context.opts.platforms.indexOf('android') === -1) {
        console.log('Skipping copy_native_code hook - not building for Android');
        return;
    }

    const projectRoot = context.opts.projectRoot;
    const sourceDir = path.join(projectRoot, 'custom_native_code');
    const targetDir = path.join(projectRoot, 'platforms', 'android', 'app', 'src', 'main', 'java');

    if (!fs.existsSync(sourceDir)) {
        console.log('Source directory does not exist:', sourceDir);
        return;
    }

    if (!fs.existsSync(targetDir)) {
        console.log('Target directory does not exist:', targetDir);
        return;
    }

    // Function to recursively copy files
    function copyRecursive(src, dest) {
        const stats = fs.statSync(src);
        
        if (stats.isDirectory()) {
            // Create directory if it doesn't exist
            if (!fs.existsSync(dest)) {
                fs.mkdirSync(dest, { recursive: true });
                console.log('Created directory:', dest);
            }
            
            // Copy all files in directory
            const files = fs.readdirSync(src);
            files.forEach(file => {
                const srcFile = path.join(src, file);
                const destFile = path.join(dest, file);
                copyRecursive(srcFile, destFile);
            });
        } else {
            // Copy file
            fs.copyFileSync(src, dest);
            console.log('Copied file:', dest);
        }
    }

    try {
        // Copy all Java files from custom_native_code (excluding the android subfolder)
        const items = fs.readdirSync(sourceDir);
        
        items.forEach(item => {
            const srcPath = path.join(sourceDir, item);
            const stats = fs.statSync(srcPath);
            
            if (stats.isDirectory()) {
                if (item === 'android') {
                    // Special handling for android folder - copy its contents to the target
                    const androidSrcPath = path.join(sourceDir, 'android');
                    if (fs.existsSync(androidSrcPath)) {
                        copyRecursive(androidSrcPath, targetDir);
                    }
                } else {
                    // Copy other directories to com/ctfs/wicimobile/
                    const destPath = path.join(targetDir, 'com', 'ctfs', 'wicimobile', item);
                    copyRecursive(srcPath, destPath);
                }
            } else if (item.endsWith('.java')) {
                // Copy individual Java files to com/ctfs/wicimobile/
                const destPath = path.join(targetDir, 'com', 'ctfs', 'wicimobile', item);
                fs.copyFileSync(srcPath, destPath);
                console.log('Copied file:', destPath);
            }
        });

        console.log('Successfully copied custom native code to Android platform');
    } catch (error) {
        console.error('Error copying custom native code:', error);
        throw error;
    }
};
