-- Merging decision tree log ---
provider#androidx.core.content.FileProvider
INJECTED from C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:12:9-14:20
	android:grantUriPermissions
		ADDED from C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:12:106-140
	android:authorities
		INJECTED from C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml
		ADDED from C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:12:19-80
	android:exported
		ADDED from C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:12:81-105
	android:name
		ADDED from C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:12:141-190
manifest
ADDED from C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:2:1-21:12
INJECTED from C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:2:1-21:12
INJECTED from C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:2:1-21:12
INJECTED from C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:2:1-21:12
MERGED from [:CordovaLib] C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\CordovaLib\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:1-27:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecfd5e644230b94874a76c80c1820e6c\transformed\jetified-appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\459cb14f885dfe9984b3b738a7946fbf\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-splashscreen:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bc56410b7c5a5d388fa8f13fb6c4a8e0\transformed\jetified-core-splashscreen-1.0.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\e341268c45f3677c44ac83ed6a3d8b90\transformed\fragment-1.5.4\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d3cbc0478ee03bd5dbdcedc24eb2e93\transformed\jetified-activity-1.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\96c70c3d1dba4d8578a16f692d4b2f06\transformed\jetified-core-ktx-1.13.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7e29fd16ac71b7b77b37ddbd84863288\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\60e725c42aa0e39aed1872784e60eecd\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\537fe04c7b6e13d2c64226c263643d00\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.webkit:webkit:1.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e101d13b04a078c4000268fbfee76b8a\transformed\webkit-1.12.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae36db20c11d60d0d0c5e114826e138f\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a5eaad206a4f3953b71e842b47d9c85f\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ce1061cc3473ea7c2d9b350e2e67410\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\820dc7349e37fa053a059f699885ed72\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4f1e8ca280e441b8f2e2c617f820de4c\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7c013e1f43d1815644395c70a32bee83\transformed\core-1.13.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad74f5f30c77bdf59fe46c71c09a16ab\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\99b65b6903a6e8433b2a3844be551a6d\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\43d493f403f3ed0382eb0f1c07b66ab5\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a03d1166253d23bd899bbc2cb32db6c2\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab3905b91a4dd3e38eac06eadbd3690\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7dfaa1a4536e60a20f1c5de2e39b6854\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e6f9eb0e49b6f30737a3e7fa5fb4c5d\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f410851a6a3ac115f37a07b4dfef2aa6\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\db5f26765e9b1ffa7eb86d656f45a8e8\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\42e3f4e99d8fe2fc7fff39091a76a22b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d9783f45c8cebffadd8c43ab7e08678\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc81222e54a596f885e120b05a8beb65\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\195757580e916ad5a9a5967927d36032\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\36c288d822d7c9865774a6f7adf8d424\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f47f3aa03c8c03de42f458f5fd2260a\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
	package
		INJECTED from C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml
	android:versionName
		ADDED from C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:2:74-101
		INJECTED from C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml
	android:hardwareAccelerated
		ADDED from C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:2:11-45
	xmlns:android
		ADDED from C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:2:102-160
	android:versionCode
		ADDED from C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:2:46-73
		INJECTED from C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml
supports-screens
ADDED from C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:3:5-191
	android:largeScreens
		ADDED from C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:3:49-76
	android:smallScreens
		ADDED from C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:3:132-159
	android:normalScreens
		ADDED from C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:3:77-105
	android:xlargeScreens
		ADDED from C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:3:160-188
	android:resizeable
		ADDED from C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:3:106-131
	android:anyDensity
		ADDED from C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:3:23-48
uses-permission#android.permission.INTERNET
ADDED from C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:4:5-67
	android:name
		ADDED from C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:4:22-64
application
ADDED from C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:5:5-15:19
INJECTED from C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:5:5-15:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\537fe04c7b6e13d2c64226c263643d00\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\537fe04c7b6e13d2c64226c263643d00\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7c013e1f43d1815644395c70a32bee83\transformed\core-1.13.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7c013e1f43d1815644395c70a32bee83\transformed\core-1.13.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\43d493f403f3ed0382eb0f1c07b66ab5\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\43d493f403f3ed0382eb0f1c07b66ab5\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\42e3f4e99d8fe2fc7fff39091a76a22b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\42e3f4e99d8fe2fc7fff39091a76a22b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d9783f45c8cebffadd8c43ab7e08678\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d9783f45c8cebffadd8c43ab7e08678\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\36c288d822d7c9865774a6f7adf8d424\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\36c288d822d7c9865774a6f7adf8d424\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7c013e1f43d1815644395c70a32bee83\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:5:121-147
	android:label
		ADDED from C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:5:88-120
	android:hardwareAccelerated
		ADDED from C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:5:18-52
	android:icon
		ADDED from C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:5:53-87
activity#com.ctfs.wicimobile.MainActivity
ADDED from C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:6:9-11:20
	android:label
		ADDED from C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:6:171-208
	android:launchMode
		ADDED from C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:6:209-239
	android:windowSoftInputMode
		ADDED from C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:6:314-356
	android:exported
		ADDED from C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:6:147-170
	android:configChanges
		ADDED from C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:6:19-146
	android:theme
		ADDED from C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:6:268-313
	android:name
		ADDED from C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:6:240-267
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:7:13-10:29
	android:label
		ADDED from C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:7:28-65
action#android.intent.action.MAIN
ADDED from C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:8:17-69
	android:name
		ADDED from C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:8:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:9:17-77
	android:name
		ADDED from C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:9:27-74
queries
ADDED from C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:16:5-20:15
intent#action:name:android.media.action.IMAGE_CAPTURE
ADDED from C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:17:9-19:18
action#android.media.action.IMAGE_CAPTURE
ADDED from C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:18:13-73
	android:name
		ADDED from C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:18:21-70
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:13:13-130
	android:resource
		ADDED from C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:13:75-127
	android:name
		ADDED from C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml:13:24-74
uses-sdk
INJECTED from C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml
INJECTED from C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml
MERGED from [:CordovaLib] C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\CordovaLib\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:5-44
MERGED from [:CordovaLib] C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\CordovaLib\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecfd5e644230b94874a76c80c1820e6c\transformed\jetified-appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecfd5e644230b94874a76c80c1820e6c\transformed\jetified-appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\459cb14f885dfe9984b3b738a7946fbf\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\459cb14f885dfe9984b3b738a7946fbf\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-splashscreen:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bc56410b7c5a5d388fa8f13fb6c4a8e0\transformed\jetified-core-splashscreen-1.0.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core-splashscreen:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bc56410b7c5a5d388fa8f13fb6c4a8e0\transformed\jetified-core-splashscreen-1.0.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\e341268c45f3677c44ac83ed6a3d8b90\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\e341268c45f3677c44ac83ed6a3d8b90\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d3cbc0478ee03bd5dbdcedc24eb2e93\transformed\jetified-activity-1.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d3cbc0478ee03bd5dbdcedc24eb2e93\transformed\jetified-activity-1.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\96c70c3d1dba4d8578a16f692d4b2f06\transformed\jetified-core-ktx-1.13.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\96c70c3d1dba4d8578a16f692d4b2f06\transformed\jetified-core-ktx-1.13.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7e29fd16ac71b7b77b37ddbd84863288\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7e29fd16ac71b7b77b37ddbd84863288\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\60e725c42aa0e39aed1872784e60eecd\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\60e725c42aa0e39aed1872784e60eecd\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\537fe04c7b6e13d2c64226c263643d00\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\537fe04c7b6e13d2c64226c263643d00\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.webkit:webkit:1.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e101d13b04a078c4000268fbfee76b8a\transformed\webkit-1.12.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e101d13b04a078c4000268fbfee76b8a\transformed\webkit-1.12.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae36db20c11d60d0d0c5e114826e138f\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae36db20c11d60d0d0c5e114826e138f\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a5eaad206a4f3953b71e842b47d9c85f\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a5eaad206a4f3953b71e842b47d9c85f\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ce1061cc3473ea7c2d9b350e2e67410\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ce1061cc3473ea7c2d9b350e2e67410\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\820dc7349e37fa053a059f699885ed72\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\820dc7349e37fa053a059f699885ed72\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4f1e8ca280e441b8f2e2c617f820de4c\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4f1e8ca280e441b8f2e2c617f820de4c\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7c013e1f43d1815644395c70a32bee83\transformed\core-1.13.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7c013e1f43d1815644395c70a32bee83\transformed\core-1.13.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad74f5f30c77bdf59fe46c71c09a16ab\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad74f5f30c77bdf59fe46c71c09a16ab\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\99b65b6903a6e8433b2a3844be551a6d\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\99b65b6903a6e8433b2a3844be551a6d\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\43d493f403f3ed0382eb0f1c07b66ab5\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\43d493f403f3ed0382eb0f1c07b66ab5\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a03d1166253d23bd899bbc2cb32db6c2\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a03d1166253d23bd899bbc2cb32db6c2\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab3905b91a4dd3e38eac06eadbd3690\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ab3905b91a4dd3e38eac06eadbd3690\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7dfaa1a4536e60a20f1c5de2e39b6854\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7dfaa1a4536e60a20f1c5de2e39b6854\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e6f9eb0e49b6f30737a3e7fa5fb4c5d\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e6f9eb0e49b6f30737a3e7fa5fb4c5d\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f410851a6a3ac115f37a07b4dfef2aa6\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f410851a6a3ac115f37a07b4dfef2aa6\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\db5f26765e9b1ffa7eb86d656f45a8e8\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\db5f26765e9b1ffa7eb86d656f45a8e8\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\42e3f4e99d8fe2fc7fff39091a76a22b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\42e3f4e99d8fe2fc7fff39091a76a22b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d9783f45c8cebffadd8c43ab7e08678\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d9783f45c8cebffadd8c43ab7e08678\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc81222e54a596f885e120b05a8beb65\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc81222e54a596f885e120b05a8beb65\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\195757580e916ad5a9a5967927d36032\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\195757580e916ad5a9a5967927d36032\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\36c288d822d7c9865774a6f7adf8d424\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\36c288d822d7c9865774a6f7adf8d424\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f47f3aa03c8c03de42f458f5fd2260a\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f47f3aa03c8c03de42f458f5fd2260a\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Projects\WICI_UpgradeStack_AI\MyApp\platforms\android\app\src\main\AndroidManifest.xml
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\537fe04c7b6e13d2c64226c263643d00\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\43d493f403f3ed0382eb0f1c07b66ab5\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\43d493f403f3ed0382eb0f1c07b66ab5\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\42e3f4e99d8fe2fc7fff39091a76a22b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\42e3f4e99d8fe2fc7fff39091a76a22b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d9783f45c8cebffadd8c43ab7e08678\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d9783f45c8cebffadd8c43ab7e08678\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\537fe04c7b6e13d2c64226c263643d00\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\537fe04c7b6e13d2c64226c263643d00\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\537fe04c7b6e13d2c64226c263643d00\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\537fe04c7b6e13d2c64226c263643d00\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\537fe04c7b6e13d2c64226c263643d00\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\537fe04c7b6e13d2c64226c263643d00\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\537fe04c7b6e13d2c64226c263643d00\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7c013e1f43d1815644395c70a32bee83\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7c013e1f43d1815644395c70a32bee83\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7c013e1f43d1815644395c70a32bee83\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
permission#com.ctfs.wicimobile.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7c013e1f43d1815644395c70a32bee83\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7c013e1f43d1815644395c70a32bee83\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7c013e1f43d1815644395c70a32bee83\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7c013e1f43d1815644395c70a32bee83\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7c013e1f43d1815644395c70a32bee83\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
uses-permission#com.ctfs.wicimobile.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7c013e1f43d1815644395c70a32bee83\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7c013e1f43d1815644395c70a32bee83\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\43d493f403f3ed0382eb0f1c07b66ab5\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\43d493f403f3ed0382eb0f1c07b66ab5\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\43d493f403f3ed0382eb0f1c07b66ab5\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\42e3f4e99d8fe2fc7fff39091a76a22b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\42e3f4e99d8fe2fc7fff39091a76a22b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\42e3f4e99d8fe2fc7fff39091a76a22b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\42e3f4e99d8fe2fc7fff39091a76a22b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\42e3f4e99d8fe2fc7fff39091a76a22b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\42e3f4e99d8fe2fc7fff39091a76a22b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\42e3f4e99d8fe2fc7fff39091a76a22b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\42e3f4e99d8fe2fc7fff39091a76a22b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\42e3f4e99d8fe2fc7fff39091a76a22b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\42e3f4e99d8fe2fc7fff39091a76a22b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\42e3f4e99d8fe2fc7fff39091a76a22b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\42e3f4e99d8fe2fc7fff39091a76a22b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\42e3f4e99d8fe2fc7fff39091a76a22b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\42e3f4e99d8fe2fc7fff39091a76a22b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\42e3f4e99d8fe2fc7fff39091a76a22b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\42e3f4e99d8fe2fc7fff39091a76a22b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\42e3f4e99d8fe2fc7fff39091a76a22b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\42e3f4e99d8fe2fc7fff39091a76a22b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\42e3f4e99d8fe2fc7fff39091a76a22b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\42e3f4e99d8fe2fc7fff39091a76a22b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\42e3f4e99d8fe2fc7fff39091a76a22b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
